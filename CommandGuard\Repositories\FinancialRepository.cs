using CommandGuard.Data;
using CommandGuard.Interfaces.Repositories;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Repositories;

/// <summary>
/// 财务记录仓储实现类，提供财务记录相关的数据访问方法
/// </summary>
public class FinancialRepository(AppDbContext dbContext, ILogger<FinancialRepository> logger)
    : BaseRepository<Financial>(dbContext, logger), IFinancialRepository
{
    /// <summary>
    /// 根据账号获取财务记录列表
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <returns>该账号的财务记录列表</returns>
    public async Task<IEnumerable<Financial>> GetFinancialsByAccountAsync(string account)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"根据账号获取财务记录列表: {Account}", account);
            return await DbContext.Financials
                .Where(f => f.Account == account)
                .OrderByDescending(f => f.<PERSON>t)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据账号获取财务记录列表失败: {Account}", account);
            return new List<Financial>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 根据财务类型获取记录列表
    /// </summary>
    /// <param name="type">财务类型</param>
    /// <returns>指定类型的财务记录列表</returns>
    public async Task<IEnumerable<Financial>> GetFinancialsByTypeAsync(CommandGuard.Enums.EnumFinancialType type)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"根据财务类型获取记录列表: {Type}", type);
            return await DbContext.Financials
                .Where(f => f.Type == type)
                .OrderByDescending(f => f.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据财务类型获取记录列表失败: {Type}", type);
            return new List<Financial>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 获取指定时间范围内的财务记录
    /// </summary>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>符合条件的财务记录列表</returns>
    public async Task<IEnumerable<Financial>> GetFinancialsByDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"获取指定时间范围内的财务记录: {StartDate} - {EndDate}", startDate, endDate);
            return await DbContext.Financials
                .Where(f => f.CreatedAt >= startDate && f.CreatedAt <= endDate)
                .OrderByDescending(f => f.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"获取指定时间范围内的财务记录失败: {StartDate} - {EndDate}", startDate, endDate);
            return new List<Financial>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 根据账号和类型获取财务记录
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <param name="type">财务类型</param>
    /// <returns>符合条件的财务记录列表</returns>
    public async Task<IEnumerable<Financial>> GetFinancialsByAccountAndTypeAsync(string account, CommandGuard.Enums.EnumFinancialType type)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"根据账号和类型获取财务记录: {Account}, {Type}", account, type);
            return await DbContext.Financials
                .Where(f => f.Account == account && f.Type == type)
                .OrderByDescending(f => f.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据账号和类型获取财务记录失败: {Account}, {Type}", account, type);
            return new List<Financial>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 根据关联业务ID获取财务记录
    /// </summary>
    /// <param name="referenceId">关联业务ID</param>
    /// <returns>关联的财务记录列表</returns>
    public async Task<IEnumerable<Financial>> GetFinancialsByReferenceIdAsync(long referenceId)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"根据关联业务ID获取财务记录: {ReferenceId}", referenceId);
            return await DbContext.Financials
                .Where(f => f.ReferenceId == referenceId)
                .OrderByDescending(f => f.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据关联业务ID获取财务记录失败: {ReferenceId}", referenceId);
            return new List<Financial>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 获取账号的财务统计信息
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>财务统计信息</returns>
    public async Task<(decimal TotalIncome, decimal TotalExpense, decimal NetAmount)> GetFinancialStatisticsByAccountAsync(string account, DateTime startDate, DateTime endDate)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"获取账号的财务统计信息: {Account}, {StartDate} - {EndDate}", account, startDate, endDate);

            var records = await DbContext.Financials
                .Where(f => f.Account == account && f.CreatedAt >= startDate && f.CreatedAt <= endDate)
                .ToListAsync();

            var totalIncome = records.Where(f => f.Amount > 0).Sum(f => f.Amount);
            var totalExpense = Math.Abs(records.Where(f => f.Amount < 0).Sum(f => f.Amount));
            var netAmount = totalIncome - totalExpense;

            return (totalIncome, totalExpense, netAmount);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"获取账号的财务统计信息失败: {Account}", account);
            return (0, 0, 0);
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 获取指定类型的财务统计信息
    /// </summary>
    /// <param name="type">财务类型</param>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>财务统计信息</returns>
    public async Task<(decimal TotalAmount, int TotalCount)> GetFinancialStatisticsByTypeAsync(CommandGuard.Enums.EnumFinancialType type, DateTime startDate, DateTime endDate)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"获取指定类型的财务统计信息: {Type}, {StartDate} - {EndDate}", type, startDate, endDate);

            var records = await DbContext.Financials
                .Where(f => f.Type == type && f.CreatedAt >= startDate && f.CreatedAt <= endDate)
                .ToListAsync();

            var totalAmount = records.Sum(f => f.Amount);
            var totalCount = records.Count;

            return (totalAmount, totalCount);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"获取指定类型的财务统计信息失败: {Type}", type);
            return (0, 0);
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 创建财务记录（带余额验证）
    /// </summary>
    /// <param name="financial">财务记录</param>
    /// <returns>创建的财务记录</returns>
    public async Task<Financial> CreateFinancialRecordAsync(Financial financial)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"创建财务记录: {Account}, {Type}, {Amount}", financial.Account, financial.Type, financial.Amount);

            // 验证余额逻辑
            if (financial.BalanceAfter != financial.BalanceBefore + financial.Amount)
            {
                throw new InvalidOperationException(@"余额计算错误：变更后余额不等于变更前余额加上变更金额");
            }

            SetTimestamps(financial, isUpdate: false);
            var result = await DbContext.InsertFinancial.AppendData(financial).ExecuteInsertedAsync();
            return result.First();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"创建财务记录失败: {Account}", financial.Account);
            throw;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 获取账号最新的余额记录
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <returns>最新的财务记录，用于获取当前余额</returns>
    public async Task<Financial?> GetLatestFinancialByAccountAsync(string account)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"获取账号最新的余额记录: {Account}", account);
            return await DbContext.Financials
                .Where(f => f.Account == account)
                .OrderByDescending(f => f.CreatedAt)
                .FirstAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"获取账号最新的余额记录失败: {Account}", account);
            return null;
        }
        finally
        {
            Semaphore.Release();
        }
    }
