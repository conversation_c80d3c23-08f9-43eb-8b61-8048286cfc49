using CommandGuard.Interfaces.Repositories;
using CommandGuard.Interfaces.Services;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services;

/// <summary>
/// 消息服务实现类，提供消息相关的业务逻辑
/// </summary>
public class MessageService(
    ILogger<MessageService> logger,
    IMessageRepository messageRepository,
    IMemberRepository memberRepository
) : IMessageService
{
    /// <summary>
    /// 保存消息
    /// </summary>
    /// <param name="message">消息对象</param>
    /// <returns>保存的消息对象</returns>
    public async Task<Models.Message> SaveMessageAsync(Models.Message message)
    {
        try
        {
            logger.LogInformation(@"保存消息: 发送者={Account}, 内容长度={ContentLength}", message.Account, message.Content.Length);

            // 验证消息内容
            if (string.IsNullOrWhiteSpace(message.Content))
            {
                throw new ArgumentException(@"消息内容不能为空");
            }

            if (string.IsNullOrWhiteSpace(message.Account))
            {
                throw new ArgumentException(@"发送者账号不能为空");
            }

            // 设置默认值
            message.IsRead = false;
            message.CreatedAt = DateTime.Now;
            message.UpdatedAt = DateTime.Now;

            var savedMessage = await messageRepository.AddAsync(message);
            logger.LogInformation(@"消息保存成功: ID={MessageId}", savedMessage.Id);

            return savedMessage;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"保存消息失败: 发送者={Account}", message.Account);
            throw;
        }
    }

    /// <summary>
    /// 获取所有未读消息
    /// </summary>
    /// <returns>未读消息列表</returns>
    public async Task<IEnumerable<Models.Message>> GetUnreadMessagesAsync()
    {
        try
        {
            logger.LogInformation(@"获取所有未读消息");
            return await messageRepository.GetUnreadMessagesAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取所有未读消息失败");
            throw;
        }
    }

    /// <summary>
    /// 根据账号获取消息列表
    /// </summary>
    /// <param name="account">发送者账号</param>
    /// <returns>该账号的消息列表</returns>
    public async Task<IEnumerable<Models.Message>> GetMessagesByAccountAsync(string account)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(account))
            {
                throw new ArgumentException(@"账号不能为空");
            }

            logger.LogInformation(@"根据账号获取消息列表: {Account}", account);
            return await messageRepository.GetMessagesByAccountAsync(account);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"根据账号获取消息列表失败: {Account}", account);
            throw;
        }
    }

    /// <summary>
    /// 标记消息为已读
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <returns>操作成功返回true，否则返回false</returns>
    public async Task<bool> MarkMessageAsReadAsync(long messageId)
    {
        try
        {
            logger.LogInformation(@"标记消息为已读: {MessageId}", messageId);

            if (messageId <= 0)
            {
                throw new ArgumentException(@"消息ID无效");
            }

            return await messageRepository.MarkAsReadAsync(messageId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"标记消息为已读失败: {MessageId}", messageId);
            throw;
        }
    }

    /// <summary>
    /// 批量标记消息为已读
    /// </summary>
    /// <param name="messageIds">消息ID列表</param>
    /// <returns>成功标记的数量</returns>
    public async Task<int> BatchMarkMessagesAsReadAsync(IEnumerable<long> messageIds)
    {
        try
        {
            var idList = messageIds.ToList();
            logger.LogInformation(@"批量标记消息为已读，数量: {Count}", idList.Count);

            if (!idList.Any())
            {
                return 0;
            }

            return await messageRepository.BatchMarkAsReadAsync(idList);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"批量标记消息为已读失败");
            throw;
        }
    }

    /// <summary>
    /// 根据内容关键字搜索消息
    /// </summary>
    /// <param name="keyword">搜索关键字</param>
    /// <returns>包含关键字的消息列表</returns>
    public async Task<IEnumerable<Models.Message>> SearchMessagesAsync(string keyword)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(keyword))
            {
                throw new ArgumentException(@"搜索关键字不能为空");
            }

            logger.LogInformation(@"根据内容关键字搜索消息: {Keyword}", keyword);
            return await messageRepository.SearchMessagesByContentAsync(keyword);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"根据内容关键字搜索消息失败: {Keyword}", keyword);
            throw;
        }
    }

    /// <summary>
    /// 获取未读消息数量
    /// </summary>
    /// <returns>未读消息数量</returns>
    public async Task<int> GetUnreadMessageCountAsync()
    {
        try
        {
            logger.LogInformation(@"获取未读消息数量");
            return await messageRepository.GetUnreadMessageCountAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取未读消息数量失败");
            throw;
        }
    }

    /// <summary>
    /// 删除消息
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <returns>删除成功返回true，否则返回false</returns>
    public async Task<bool> DeleteMessageAsync(long messageId)
    {
        try
        {
            if (messageId <= 0)
            {
                throw new ArgumentException(@"消息ID无效");
            }

            logger.LogInformation(@"删除消息: {MessageId}", messageId);
            return await messageRepository.DeleteAsync(messageId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"删除消息失败: {MessageId}", messageId);
            throw;
        }
    }

    /// <summary>
    /// 获取指定时间范围内的消息
    /// </summary>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>符合条件的消息列表</returns>
    public async Task<IEnumerable<Models.Message>> GetMessagesByDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        try
        {
            if (startDate > endDate)
            {
                throw new ArgumentException(@"开始时间不能大于结束时间");
            }

            logger.LogInformation(@"获取指定时间范围内的消息: {StartDate} - {EndDate}", startDate, endDate);
            return await messageRepository.GetMessagesByDateRangeAsync(startDate, endDate);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取指定时间范围内的消息失败: {StartDate} - {EndDate}", startDate, endDate);
            throw;
        }
    }