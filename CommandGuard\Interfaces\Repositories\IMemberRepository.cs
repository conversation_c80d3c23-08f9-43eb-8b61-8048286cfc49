using CommandGuard.Models;

namespace CommandGuard.Interfaces.Repositories;

/// <summary>
/// 成员仓储接口，提供成员相关的数据访问方法
/// </summary>
public interface IMemberRepository : IRepository<Member>
{
    /// <summary>
    /// 根据账号获取成员信息
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <returns>成员信息，不存在时返回null</returns>
    Task<Member?> GetByAccountAsync(string account);

    /// <summary>
    /// 检查账号是否已存在
    /// </summary>
    /// <param name="account">要检查的账号</param>
    /// <returns>存在返回true，否则返回false</returns>
    Task<bool> AccountExistsAsync(string account);

    /// <summary>
    /// 更新成员余额
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <param name="newBalance">新余额</param>
    /// <returns>更新成功返回true，否则返回false</returns>
    Task<bool> UpdateBalanceAsync(string account, decimal newBalance);

    /// <summary>
    /// 根据代理名称获取下级成员列表
    /// </summary>
    /// <param name="agentName">代理名称</param>
    /// <returns>下级成员列表</returns>
    Task<IEnumerable<Member>> GetMembersByAgentAsync(string agentName);

    /// <summary>
    /// 获取余额大于指定金额的成员列表
    /// </summary>
    /// <param name="minBalance">最小余额</param>
    /// <returns>符合条件的成员列表</returns>
    Task<IEnumerable<Member>> GetMembersByMinBalanceAsync(decimal minBalance);

    /// <summary>
    /// 获取指定时间范围内注册的成员列表
    /// </summary>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>符合条件的成员列表</returns>
    Task<IEnumerable<Member>> GetMembersByDateRangeAsync(DateTime startDate, DateTime endDate);

    /// <summary>
    /// 批量更新成员信息
    /// </summary>
    /// <param name="members">要更新的成员列表</param>
    /// <returns>更新成功的数量</returns>
    Task<int> BatchUpdateAsync(IEnumerable<Member> members);
}
