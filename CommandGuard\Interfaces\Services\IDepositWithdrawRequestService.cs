using CommandGuard.Models;

namespace CommandGuard.Interfaces.Services;

/// <summary>
/// 存取款申请服务接口，提供存取款申请相关的业务逻辑
/// </summary>
public interface IDepositWithdrawRequestService
{
    /// <summary>
    /// 创建存取款申请
    /// </summary>
    /// <param name="request">申请信息</param>
    /// <returns>创建的申请对象</returns>
    Task<DepositWithdrawRequest> CreateRequestAsync(DepositWithdrawRequest request);

    /// <summary>
    /// 根据账号获取申请列表
    /// </summary>
    /// <param name="account">申请人账号</param>
    /// <returns>该账号的申请列表</returns>
    Task<IEnumerable<DepositWithdrawRequest>> GetRequestsByAccountAsync(string account);

    /// <summary>
    /// 获取待审核的申请列表
    /// </summary>
    /// <returns>待审核的申请列表</returns>
    Task<IEnumerable<DepositWithdrawRequest>> GetPendingRequestsAsync();

    /// <summary>
    /// 审核申请
    /// </summary>
    /// <param name="requestId">申请ID</param>
    /// <param name="isApproved">是否批准</param>
    /// <returns>审核成功返回true，否则返回false</returns>
    Task<bool> ApproveRequestAsync(long requestId, bool isApproved);

    /// <summary>
    /// 批量审核申请
    /// </summary>
    /// <param name="requestIds">申请ID列表</param>
    /// <param name="isApproved">是否批准</param>
    /// <returns>审核成功的数量</returns>
    Task<int> BatchApproveRequestsAsync(IEnumerable<long> requestIds, bool isApproved);

    /// <summary>
    /// 根据消息ID获取申请
    /// </summary>
    /// <param name="messageId">关联的消息ID</param>
    /// <returns>关联的申请，不存在时返回null</returns>
    Task<DepositWithdrawRequest?> GetRequestByMessageIdAsync(string messageId);

    /// <summary>
    /// 验证申请
    /// </summary>
    /// <param name="request">申请信息</param>
    /// <returns>验证结果</returns>
    Task<(bool IsValid, string ErrorMessage)> ValidateRequestAsync(DepositWithdrawRequest request);

    /// <summary>
    /// 获取申请统计信息
    /// </summary>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>申请统计信息</returns>
    Task<(int PendingCount, int ApprovedCount, int RejectedCount, decimal TotalAmount)> GetRequestStatisticsAsync(DateTime startDate, DateTime endDate);

    /// <summary>
    /// 删除申请
    /// </summary>
    /// <param name="requestId">申请ID</param>
    /// <returns>删除成功返回true，否则返回false</returns>
    Task<bool> DeleteRequestAsync(long requestId);
}
