using CommandGuard.Models;

namespace CommandGuard.Interfaces.Repositories;

/// <summary>
/// 存取款申请仓储接口，提供存取款申请相关的数据访问方法
/// </summary>
public interface IDepositWithdrawRequestRepository : IRepository<DepositWithdrawRequest>
{
    /// <summary>
    /// 根据账号获取存取款申请列表
    /// </summary>
    /// <param name="account">申请人账号</param>
    /// <returns>该账号的存取款申请列表</returns>
    Task<IEnumerable<DepositWithdrawRequest>> GetRequestsByAccountAsync(string account);

    /// <summary>
    /// 根据申请类型获取申请列表
    /// </summary>
    /// <param name="requestType">申请类型</param>
    /// <returns>指定类型的申请列表</returns>
    Task<IEnumerable<DepositWithdrawRequest>> GetRequestsByTypeAsync(CommandGuard.Enums.EnumDepositWithdrawRequestType requestType);

    /// <summary>
    /// 根据状态获取申请列表
    /// </summary>
    /// <param name="status">申请状态</param>
    /// <returns>指定状态的申请列表</returns>
    Task<IEnumerable<DepositWithdrawRequest>> GetRequestsByStatusAsync(CommandGuard.Enums.EnumDepositWithdrawRequestStatus status);

    /// <summary>
    /// 获取待审核的申请列表
    /// </summary>
    /// <returns>待审核的申请列表</returns>
    Task<IEnumerable<DepositWithdrawRequest>> GetPendingRequestsAsync();

    /// <summary>
    /// 根据账号和状态获取申请列表
    /// </summary>
    /// <param name="account">申请人账号</param>
    /// <param name="status">申请状态</param>
    /// <returns>符合条件的申请列表</returns>
    Task<IEnumerable<DepositWithdrawRequest>> GetRequestsByAccountAndStatusAsync(string account, CommandGuard.Enums.EnumDepositWithdrawRequestStatus status);

    /// <summary>
    /// 获取指定时间范围内的申请
    /// </summary>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>符合条件的申请列表</returns>
    Task<IEnumerable<DepositWithdrawRequest>> GetRequestsByDateRangeAsync(DateTime startDate, DateTime endDate);

    /// <summary>
    /// 审核申请
    /// </summary>
    /// <param name="requestId">申请ID</param>
    /// <param name="status">审核结果状态</param>
    /// <returns>更新成功返回true，否则返回false</returns>
    Task<bool> ApproveRequestAsync(long requestId, CommandGuard.Enums.EnumDepositWithdrawRequestStatus status);

    /// <summary>
    /// 根据消息ID获取申请
    /// </summary>
    /// <param name="messageId">关联的消息ID</param>
    /// <returns>关联的申请，不存在时返回null</returns>
    Task<DepositWithdrawRequest?> GetRequestByMessageIdAsync(string messageId);

    /// <summary>
    /// 获取申请统计信息
    /// </summary>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>申请统计信息</returns>
    Task<(int PendingCount, int ApprovedCount, int RejectedCount, decimal TotalAmount)> GetRequestStatisticsAsync(DateTime startDate, DateTime endDate);
}
