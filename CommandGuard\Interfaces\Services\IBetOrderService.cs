
using CommandGuard.Models;

namespace CommandGuard.Interfaces.Services;

/// <summary>
/// 投注订单服务接口，提供投注订单相关的业务逻辑
/// </summary>
public interface IBetOrderService
{
    /// <summary>
    /// 创建投注订单
    /// </summary>
    /// <param name="betOrder">投注订单信息</param>
    /// <returns>创建的投注订单对象</returns>
    Task<BetOrder> CreateBetOrderAsync(BetOrder betOrder);

    /// <summary>
    /// 根据订单号获取投注订单
    /// </summary>
    /// <param name="orderNumber">订单号</param>
    /// <returns>投注订单，不存在时返回null</returns>
    Task<BetOrder?> GetBetOrderByNumberAsync(string orderNumber);

    /// <summary>
    /// 根据账号获取投注订单列表
    /// </summary>
    /// <param name="account">投注用户账号</param>
    /// <returns>该账号的投注订单列表</returns>
    Task<IEnumerable<BetOrder>> GetBetOrdersByAccountAsync(string account);

    /// <summary>
    /// 根据期号获取投注订单列表
    /// </summary>
    /// <param name="issue">投注期号</param>
    /// <returns>该期号的投注订单列表</returns>
    Task<IEnumerable<BetOrder>> GetBetOrdersByIssueAsync(string issue);

    /// <summary>
    /// 结算投注订单
    /// </summary>
    /// <param name="issue">期号</param>
    /// <param name="drawResult">开奖结果</param>
    /// <returns>结算的订单数量</returns>
    Task<int> SettleBetOrdersAsync(string issue, string drawResult);

    /// <summary>
    /// 取消投注订单
    /// </summary>
    /// <param name="orderNumber">订单号</param>
    /// <returns>取消成功返回true，否则返回false</returns>
    Task<bool> CancelBetOrderAsync(string orderNumber);

    /// <summary>
    /// 处理回水
    /// </summary>
    /// <param name="orderNumber">订单号</param>
    /// <returns>处理成功返回true，否则返回false</returns>
    Task<bool> ProcessRebateAsync(string orderNumber);

    /// <summary>
    /// 批量处理回水
    /// </summary>
    /// <param name="issue">期号</param>
    /// <returns>处理成功的订单数量</returns>
    Task<int> BatchProcessRebateAsync(string issue);

    /// <summary>
    /// 获取投注统计信息
    /// </summary>
    /// <param name="account">账号（可选）</param>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>投注统计信息</returns>
    Task<(decimal TotalAmount, decimal TotalWin, int TotalOrders, decimal WinRate)> GetBetStatisticsAsync(string? account, DateTime startDate, DateTime endDate);

    /// <summary>
    /// 验证投注订单
    /// </summary>
    /// <param name="betOrder">投注订单</param>
    /// <returns>验证结果</returns>
    Task<(bool IsValid, string ErrorMessage)> ValidateBetOrderAsync(BetOrder betOrder);

    /// <summary>
    /// 生成订单号
    /// </summary>
    /// <returns>唯一的订单号</returns>
    Task<string> GenerateOrderNumberAsync();

    /// <summary>
    /// 检查期号是否可以投注
    /// </summary>
    /// <param name="issue">期号</param>
    /// <returns>可以投注返回true，否则返回false</returns>
    Task<bool> CanBetOnIssueAsync(string issue);
}
