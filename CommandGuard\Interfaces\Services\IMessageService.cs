using CommandGuard.Models;

namespace CommandGuard.Interfaces.Services;

/// <summary>
/// 消息服务接口，提供消息相关的业务逻辑
/// </summary>
public interface IMessageService
{
    /// <summary>
    /// 保存消息
    /// </summary>
    /// <param name="message">消息对象</param>
    /// <returns>保存的消息对象</returns>
    Task<Message> SaveMessageAsync(Message message);

    /// <summary>
    /// 获取所有未读消息
    /// </summary>
    /// <returns>未读消息列表</returns>
    Task<IEnumerable<Message>> GetUnreadMessagesAsync();

    /// <summary>
    /// 根据账号获取消息列表
    /// </summary>
    /// <param name="account">发送者账号</param>
    /// <returns>该账号的消息列表</returns>
    Task<IEnumerable<Message>> GetMessagesByAccountAsync(string account);

    /// <summary>
    /// 标记消息为已读
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <returns>操作成功返回true，否则返回false</returns>
    Task<bool> MarkMessageAsReadAsync(long messageId);

    /// <summary>
    /// 批量标记消息为已读
    /// </summary>
    /// <param name="messageIds">消息ID列表</param>
    /// <returns>成功标记的数量</returns>
    Task<int> BatchMarkMessagesAsReadAsync(IEnumerable<long> messageIds);

    /// <summary>
    /// 根据内容关键字搜索消息
    /// </summary>
    /// <param name="keyword">搜索关键字</param>
    /// <returns>包含关键字的消息列表</returns>
    Task<IEnumerable<Message>> SearchMessagesAsync(string keyword);

    /// <summary>
    /// 获取未读消息数量
    /// </summary>
    /// <returns>未读消息数量</returns>
    Task<int> GetUnreadMessageCountAsync();

    /// <summary>
    /// 删除消息
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <returns>删除成功返回true，否则返回false</returns>
    Task<bool> DeleteMessageAsync(long messageId);

    /// <summary>
    /// 获取指定时间范围内的消息
    /// </summary>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>符合条件的消息列表</returns>
    Task<IEnumerable<Message>> GetMessagesByDateRangeAsync(DateTime startDate, DateTime endDate);
}
