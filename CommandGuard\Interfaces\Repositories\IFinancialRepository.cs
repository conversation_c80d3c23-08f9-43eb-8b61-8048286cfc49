using CommandGuard.Models;

namespace CommandGuard.Interfaces.Repositories;

/// <summary>
/// 财务记录仓储接口，提供财务记录相关的数据访问方法
/// </summary>
public interface IFinancialRepository : IRepository<Financial>
{
    /// <summary>
    /// 根据账号获取财务记录列表
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <returns>该账号的财务记录列表</returns>
    Task<IEnumerable<Financial>> GetFinancialsByAccountAsync(string account);

    /// <summary>
    /// 根据财务类型获取记录列表
    /// </summary>
    /// <param name="type">财务类型</param>
    /// <returns>指定类型的财务记录列表</returns>
    Task<IEnumerable<Financial>> GetFinancialsByTypeAsync(CommandGuard.Enums.EnumFinancialType type);

    /// <summary>
    /// 获取指定时间范围内的财务记录
    /// </summary>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>符合条件的财务记录列表</returns>
    Task<IEnumerable<Financial>> GetFinancialsByDateRangeAsync(DateTime startDate, DateTime endDate);

    /// <summary>
    /// 根据账号和类型获取财务记录
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <param name="type">财务类型</param>
    /// <returns>符合条件的财务记录列表</returns>
    Task<IEnumerable<Financial>> GetFinancialsByAccountAndTypeAsync(string account, CommandGuard.Enums.EnumFinancialType type);

    /// <summary>
    /// 根据关联业务ID获取财务记录
    /// </summary>
    /// <param name="referenceId">关联业务ID</param>
    /// <returns>关联的财务记录列表</returns>
    Task<IEnumerable<Financial>> GetFinancialsByReferenceIdAsync(long referenceId);

    /// <summary>
    /// 获取账号的财务统计信息
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>财务统计信息</returns>
    Task<(decimal TotalIncome, decimal TotalExpense, decimal NetAmount)> GetFinancialStatisticsByAccountAsync(string account, DateTime startDate, DateTime endDate);

    /// <summary>
    /// 获取指定类型的财务统计信息
    /// </summary>
    /// <param name="type">财务类型</param>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>财务统计信息</returns>
    Task<(decimal TotalAmount, int TotalCount)> GetFinancialStatisticsByTypeAsync(CommandGuard.Enums.EnumFinancialType type, DateTime startDate, DateTime endDate);

    /// <summary>
    /// 创建财务记录（带余额验证）
    /// </summary>
    /// <param name="financial">财务记录</param>
    /// <returns>创建的财务记录</returns>
    Task<Financial> CreateFinancialRecordAsync(Financial financial);

    /// <summary>
    /// 获取账号最新的余额记录
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <returns>最新的财务记录，用于获取当前余额</returns>
    Task<Financial?> GetLatestFinancialByAccountAsync(string account);
}

