using CommandGuard.Models;

namespace CommandGuard.Interfaces.Services;

/// <summary>
/// 财务服务接口，提供财务相关的业务逻辑
/// </summary>
public interface IFinancialService
{
    /// <summary>
    /// 创建财务记录
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <param name="type">财务类型</param>
    /// <param name="amount">变更金额</param>
    /// <param name="referenceId">关联业务ID</param>
    /// <returns>创建的财务记录</returns>
    Task<Financial> CreateFinancialRecordAsync(string account, CommandGuard.Enums.EnumFinancialType type, decimal amount, long referenceId = 0);

    /// <summary>
    /// 根据账号获取财务记录列表
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <returns>该账号的财务记录列表</returns>
    Task<IEnumerable<Financial>> GetFinancialsByAccountAsync(string account);

    /// <summary>
    /// 获取指定时间范围内的财务记录
    /// </summary>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>符合条件的财务记录列表</returns>
    Task<IEnumerable<Financial>> GetFinancialsByDateRangeAsync(DateTime startDate, DateTime endDate);

    /// <summary>
    /// 获取账号的财务统计信息
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>财务统计信息</returns>
    Task<(decimal TotalIncome, decimal TotalExpense, decimal NetAmount)> GetFinancialStatisticsByAccountAsync(string account, DateTime startDate, DateTime endDate);

    /// <summary>
    /// 获取系统财务统计信息
    /// </summary>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>系统财务统计信息</returns>
    Task<(decimal TotalIncome, decimal TotalExpense, decimal NetAmount, int TotalTransactions)> GetSystemFinancialStatisticsAsync(DateTime startDate, DateTime endDate);

    /// <summary>
    /// 根据类型获取财务统计信息
    /// </summary>
    /// <param name="type">财务类型</param>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>财务统计信息</returns>
    Task<(decimal TotalAmount, int TotalCount)> GetFinancialStatisticsByTypeAsync(CommandGuard.Enums.EnumFinancialType type, DateTime startDate, DateTime endDate);

    /// <summary>
    /// 验证财务操作
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <param name="amount">操作金额</param>
    /// <param name="type">财务类型</param>
    /// <returns>验证结果</returns>
    Task<(bool IsValid, string ErrorMessage)> ValidateFinancialOperationAsync(string account, decimal amount, CommandGuard.Enums.EnumFinancialType type);

    /// <summary>
    /// 获取账号当前余额
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <returns>当前余额</returns>
    Task<decimal> GetCurrentBalanceAsync(string account);
}
