﻿using System.ComponentModel.DataAnnotations;
using CommandGuard.Enums;
using FreeSql.DataAnnotations;

namespace CommandGuard.Models;

public class BetOrder
{
    #region 基本信息

    /// <summary>
    /// 投注订单唯一标识符
    /// 数据库主键，自动递增，用于唯一标识每个投注订单记录
    /// 数据类型：long - 64位整数，支持海量投注订单
    /// 用途：订单标识、关联查询、业务追踪等
    /// </summary>
    [Column(IsIdentity = true, IsPrimary = true)]
    public long Id { get; set; }

    /// <summary>
    /// 投注订单号
    /// 业务层面的唯一订单标识，用于对外展示和查询
    /// 约束：固定9位长度，不能为空，系统内必须唯一
    /// 格式：通常为数字组合，如：123456789
    /// 用途：订单查询、客户服务、报表统计、对账等
    /// </summary>
    [Required(ErrorMessage = @"订单号不能为空")]
    [StringLength(9, MinimumLength = 9, ErrorMessage = @"订单号长度必须为9位")]
    [Column(StringLength = 9, IsNullable = false)]
    public string OrderNumber { get; set; } = string.Empty;

    /// <summary>
    /// 关联投注用户账号
    /// 进行投注的成员账号，必须是系统中存在的有效成员账号
    /// 约束：最大长度100字符，不能为空，必须关联有效的成员账号
    /// 用途：投注归属、余额扣减、盈亏计算、统计分析等
    /// 关联：与Member表的Account字段关联
    /// </summary>
    [Required(ErrorMessage = @"投注用户账号不能为空")]
    [StringLength(100, ErrorMessage = @"投注用户账号长度不能超过100个字符")]
    [Column(StringLength = 100, IsNullable = false)]
    public string Account { get; set; } = string.Empty;

    #endregion

    #region 答题信息

    /// <summary>
    /// 投注期号
    /// 标识投注所属的游戏期次，用于区分不同轮次的投注
    /// 约束：最大长度50字符，不能为空
    /// 格式：通常为日期+期次，如：***********
    /// 用途：期次管理、开奖结算、统计分析等
    /// </summary>
    [Required(ErrorMessage = @"投注期号不能为空")]
    [StringLength(50, ErrorMessage = @"投注期号长度不能超过50个字符")]
    [Column(StringLength = 50, IsNullable = false)]
    public string Issue { get; set; } = string.Empty;

    /// <summary>
    /// 投注彩种
    /// 标识投注所属的彩种类型
    /// 枚举类型：EnumBetLottery
    /// 约束：不能为空
    /// 用途：彩种分类、玩法识别、赔率计算、统计分析等
    /// </summary>
    [Required(ErrorMessage = @"投注彩种不能为空")]
    [Column(IsNullable = false)]
    public EnumBetLottery BetLottery { get; set; } = EnumBetLottery.台湾宾果3;

    /// <summary>
    /// 投注玩法项目
    /// 标识具体的投注玩法类型，如：1正、2番、12角、大小、单双等
    /// 约束：最大长度20字符，不能为空
    /// 示例：1正、2番、12角、大、小、单、双、围骰等
    /// 用途：玩法识别、赔率计算、中奖判断、统计分析等
    /// </summary>
    [Required(ErrorMessage = @"投注玩法不能为空")]
    [StringLength(20, ErrorMessage = @"投注玩法长度不能超过20个字符")]
    [Column(StringLength = 20, IsNullable = false)]
    public string PlayItem { get; set; } = string.Empty;

    /// <summary>
    /// 投注时的赔率
    /// 投注时锁定的赔率，用于计算中奖金额
    /// 数据类型：decimal(8,3) - 精度8位，小数点后3位
    /// 约束：范围1.000-100.000，不能为空
    /// 示例：1.950表示1赔1.95
    /// 用途：中奖金额计算、盈亏统计、赔率分析等
    /// </summary>
    [Required(ErrorMessage = @"投注赔率不能为空")]
    [Range(1.0, 100.0, ErrorMessage = @"赔率必须在1.0-100.0之间")]
    [Column(Precision = 8, Scale = 3, IsNullable = false)]
    public decimal Odds { get; set; }

    #endregion

    #region 金额信息

    /// <summary>
    /// 投注金额
    /// 用户本次投注的金额，从用户余额中扣除
    /// 数据类型：decimal(18,2) - 精度18位，小数点后2位
    /// 约束：必须大于0.01，不能为空
    /// 范围：0.01 到 9999999999999999.99
    /// 用途：余额扣减、中奖计算、统计分析、财务对账等
    /// </summary>
    [Required(ErrorMessage = @"投注金额不能为空")]
    [Range(1, 9999999999999999.99, ErrorMessage = @"投注金额必须大于等于1")]
    [Column(Precision = 18, Scale = 2, IsNullable = false)]
    public decimal Amount { get; set; }

    /// <summary>
    /// 结算金额
    /// 订单结算后的实际金额，中奖时为投注金额×赔率，未中奖时为0
    /// 数据类型：decimal(18,2) - 精度18位，小数点后2位
    /// 约束：可为空，未结算时为null，结算后必须≥0
    /// 计算：中奖时 = Amount × Odds，未中奖时 = 0
    /// 用途：中奖派彩、余额增加、盈亏统计、财务对账等
    /// </summary>
    [Column(Precision = 18, Scale = 2, IsNullable = true)]
    public decimal? SettledAmount { get; set; }

    /// <summary>
    /// 回水金额
    /// 根据投注金额和用户回水比例计算的返点金额
    /// 数据类型：decimal(18,2) - 精度18位，小数点后2位
    /// 约束：默认值为0，≥0，只有实际发放后才有值
    /// 计算：Amount × Member.RebatePercent / 100
    /// 状态：0表示未回水，>0表示已回水
    /// 用途：返点发放、余额增加、统计分析、财务对账等
    /// </summary>
    [Column(Precision = 18, Scale = 2, IsNullable = false)]
    public decimal RebateAmount { get; set; }

    #endregion

    #region 状态和结果

    /// <summary>
    /// 订单状态
    /// 标识订单当前所处的生命周期状态
    /// 枚举类型：EnumBetOrderStatus
    /// 约束：不能为空，默认值为已确认
    /// 状态流转：已确认 → 中奖/未中奖/和局/已取消
    /// 用途：订单管理、状态控制、业务流程、统计分析等
    /// </summary>
    [Required(ErrorMessage = @"订单状态不能为空")]
    [Column(IsNullable = false)]
    public EnumBetOrderStatus Status { get; set; } = EnumBetOrderStatus.Confirmed;

    /// <summary>
    /// 开奖结果
    /// 该期次的开奖结果，用于判断投注是否中奖
    /// 约束：固定1位长度，可为空（未开奖时为空）
    /// 格式：番摊结果为1、2、3、4，其他游戏可能有不同格式
    /// 用途：中奖判断、结算计算、统计分析、历史查询等
    /// </summary>
    [StringLength(1, ErrorMessage = @"开奖结果长度必须为1位")]
    [Column(StringLength = 1, IsNullable = true)]
    public string DrawResult { get; set; } = string.Empty;

    #endregion

    #region 关联信息

    /// <summary>
    /// 订单真假状态
    /// 标识订单是真实投注还是模拟投注
    /// 约束：不能为空，默认值为true（真实订单）
    /// 业务：true表示真实投注（影响余额），false表示模拟投注（不影响余额）
    /// 用途：订单分类、财务结算、统计分析、风控管理等
    /// </summary>
    [Required(ErrorMessage = @"订单真假状态不能为空")]
    [Column(IsNullable = false)]
    public bool IsRealOrder { get; set; } = true;

    /// <summary>
    /// 关联消息ID
    /// 触发此投注订单的相关消息ID，用于消息追溯和关联查询
    /// 约束：最大长度50字符，可为空（某些订单可能不关联消息）
    /// 用途：消息追溯、关联查询、业务验证、问题排查等
    /// 关联：与Message表的相关字段关联
    /// </summary>
    [StringLength(50, ErrorMessage = @"关联消息ID长度不能超过50个字符")]
    [Column(StringLength = 50, IsNullable = true)]
    public string MessageId { get; set; } = string.Empty;

    #endregion

    #region 时间信息

    /// <summary>
    /// 订单创建时间
    /// 投注订单创建的时间戳，用于时间排序和统计分析
    /// 约束：不可为空，自动设置为当前时间，创建后不可修改
    /// 用途：时间排序、统计分析、数据归档、业务追踪等
    /// </summary>
    [Required(ErrorMessage = @"创建时间不能为空")]
    [Column(IsNullable = false, ServerTime = DateTimeKind.Local)]
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 订单结算时间
    /// 订单完成结算的时间戳，未结算时为null
    /// 约束：可为空，结算时自动设置为当前时间
    /// 用途：结算追踪、时效统计、业务分析、数据归档等
    /// </summary>
    [Column(IsNullable = true)]
    public DateTime? SettledTime { get; set; }

    /// <summary>
    /// 回水发放时间
    /// 回水实际发放的时间戳，未发放时为null
    /// 约束：可为空，发放回水时自动设置为当前时间
    /// 用途：回水追踪、发放统计、业务分析、财务对账等
    /// </summary>
    [Column(IsNullable = true)]
    public DateTime? RebateTime { get; set; }

    #endregion
}