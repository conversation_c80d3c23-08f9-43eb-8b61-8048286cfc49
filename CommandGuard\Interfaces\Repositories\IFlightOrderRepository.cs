using CommandGuard.Models;

namespace CommandGuard.Interfaces.Repositories;

/// <summary>
/// 飞单订单仓储接口，提供飞单订单相关的数据访问方法
/// </summary>
public interface IFlightOrderRepository : IRepository<FlightOrder>
{
    /// <summary>
    /// 根据期号获取飞单订单列表
    /// </summary>
    /// <param name="issue">投注期号</param>
    /// <returns>该期号的飞单订单列表</returns>
    Task<IEnumerable<FlightOrder>> GetFlightOrdersByIssueAsync(string issue);

    /// <summary>
    /// 根据状态获取飞单订单列表
    /// </summary>
    /// <param name="status">飞单状态</param>
    /// <returns>指定状态的飞单订单列表</returns>
    Task<IEnumerable<FlightOrder>> GetFlightOrdersByStatusAsync(CommandGuard.Enums.EnumFlightOrderStatus status);

    /// <summary>
    /// 根据玩法获取飞单订单列表
    /// </summary>
    /// <param name="playItem">投注玩法</param>
    /// <returns>指定玩法的飞单订单列表</returns>
    Task<IEnumerable<FlightOrder>> GetFlightOrdersByPlayItemAsync(string playItem);

    /// <summary>
    /// 获取待处理的飞单订单列表
    /// </summary>
    /// <returns>待处理的飞单订单列表</returns>
    Task<IEnumerable<FlightOrder>> GetPendingFlightOrdersAsync();

    /// <summary>
    /// 根据期号和玩法获取飞单订单
    /// </summary>
    /// <param name="issue">投注期号</param>
    /// <param name="playItem">投注玩法</param>
    /// <returns>符合条件的飞单订单列表</returns>
    Task<IEnumerable<FlightOrder>> GetFlightOrdersByIssueAndPlayItemAsync(string issue, string playItem);

    /// <summary>
    /// 获取指定时间范围内的飞单订单
    /// </summary>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>符合条件的飞单订单列表</returns>
    Task<IEnumerable<FlightOrder>> GetFlightOrdersByDateRangeAsync(DateTime startDate, DateTime endDate);

    /// <summary>
    /// 更新飞单状态
    /// </summary>
    /// <param name="flightOrderId">飞单订单ID</param>
    /// <param name="status">新状态</param>
    /// <returns>更新成功返回true，否则返回false</returns>
    Task<bool> UpdateFlightOrderStatusAsync(long flightOrderId, CommandGuard.Enums.EnumFlightOrderStatus status);

    /// <summary>
    /// 批量更新飞单状态
    /// </summary>
    /// <param name="flightOrderIds">飞单订单ID列表</param>
    /// <param name="status">新状态</param>
    /// <returns>更新成功的数量</returns>
    Task<int> BatchUpdateFlightOrderStatusAsync(IEnumerable<long> flightOrderIds, CommandGuard.Enums.EnumFlightOrderStatus status);

    /// <summary>
    /// 获取飞单统计信息
    /// </summary>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>飞单统计信息</returns>
    Task<(int PendingCount, int ProcessingCount, int SuccessCount, int FailedCount, decimal TotalAmount)> GetFlightOrderStatisticsAsync(DateTime startDate, DateTime endDate);
}
