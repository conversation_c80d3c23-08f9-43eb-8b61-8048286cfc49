using CommandGuard.Data;
using CommandGuard.Interfaces.Repositories;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Repositories;

/// <summary>
/// 存取款申请仓储实现类，提供存取款申请相关的数据访问方法
/// </summary>
public class DepositWithdrawRequestRepository(AppDbContext dbContext, ILogger<DepositWithdrawRequestRepository> logger)
    : BaseRepository<DepositWithdrawRequest>(dbContext, logger), IDepositWithdrawRequestRepository
{
    /// <summary>
    /// 根据账号获取存取款申请列表
    /// </summary>
    /// <param name="account">申请人账号</param>
    /// <returns>该账号的存取款申请列表</returns>
    public async Task<IEnumerable<DepositWithdrawRequest>> GetRequestsByAccountAsync(string account)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"根据账号获取存取款申请列表: {Account}", account);
            return await DbContext.DepositWithdrawRequests
                .Where(r => r.Account == account)
                .OrderByDescending(r => r.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据账号获取存取款申请列表失败: {Account}", account);
            return new List<DepositWithdrawRequest>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 根据申请类型获取申请列表
    /// </summary>
    /// <param name="requestType">申请类型</param>
    /// <returns>指定类型的申请列表</returns>
    public async Task<IEnumerable<DepositWithdrawRequest>> GetRequestsByTypeAsync(CommandGuard.Enums.EnumDepositWithdrawRequestType requestType)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"根据申请类型获取申请列表: {RequestType}", requestType);
            return await DbContext.DepositWithdrawRequests
                .Where(r => r.RequestType == requestType)
                .OrderByDescending(r => r.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据申请类型获取申请列表失败: {RequestType}", requestType);
            return new List<DepositWithdrawRequest>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 根据状态获取申请列表
    /// </summary>
    /// <param name="status">申请状态</param>
    /// <returns>指定状态的申请列表</returns>
    public async Task<IEnumerable<DepositWithdrawRequest>> GetRequestsByStatusAsync(CommandGuard.Enums.EnumDepositWithdrawRequestStatus status)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"根据状态获取申请列表: {Status}", status);
            return await DbContext.DepositWithdrawRequests
                .Where(r => r.Status == status)
                .OrderByDescending(r => r.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据状态获取申请列表失败: {Status}", status);
            return new List<DepositWithdrawRequest>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 获取待审核的申请列表
    /// </summary>
    /// <returns>待审核的申请列表</returns>
    public async Task<IEnumerable<DepositWithdrawRequest>> GetPendingRequestsAsync()
    {
        return await GetRequestsByStatusAsync(CommandGuard.Enums.EnumDepositWithdrawRequestStatus.Pending);
    }

    /// <summary>
    /// 根据账号和状态获取申请列表
    /// </summary>
    /// <param name="account">申请人账号</param>
    /// <param name="status">申请状态</param>
    /// <returns>符合条件的申请列表</returns>
    public async Task<IEnumerable<DepositWithdrawRequest>> GetRequestsByAccountAndStatusAsync(string account, CommandGuard.Enums.EnumDepositWithdrawRequestStatus status)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"根据账号和状态获取申请列表: {Account}, {Status}", account, status);
            return await DbContext.DepositWithdrawRequests
                .Where(r => r.Account == account && r.Status == status)
                .OrderByDescending(r => r.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据账号和状态获取申请列表失败: {Account}, {Status}", account, status);
            return new List<DepositWithdrawRequest>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 获取指定时间范围内的申请
    /// </summary>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>符合条件的申请列表</returns>
    public async Task<IEnumerable<DepositWithdrawRequest>> GetRequestsByDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"获取指定时间范围内的申请: {StartDate} - {EndDate}", startDate, endDate);
            return await DbContext.DepositWithdrawRequests
                .Where(r => r.CreatedAt >= startDate && r.CreatedAt <= endDate)
                .OrderByDescending(r => r.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"获取指定时间范围内的申请失败: {StartDate} - {EndDate}", startDate, endDate);
            return new List<DepositWithdrawRequest>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 审核申请
    /// </summary>
    /// <param name="requestId">申请ID</param>
    /// <param name="status">审核结果状态</param>
    /// <returns>更新成功返回true，否则返回false</returns>
    public async Task<bool> ApproveRequestAsync(long requestId, CommandGuard.Enums.EnumDepositWithdrawRequestStatus status)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"审核申请: {RequestId}, {Status}", requestId, status);
            var affectedRows = await DbContext.UpdateDepositWithdrawRequest
                .Set(r => r.Status, status)
                .Set(r => r.UpdatedAt, DateTime.Now)
                .Where(r => r.Id == requestId)
                .ExecuteAffrowsAsync();
            return affectedRows > 0;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"审核申请失败: {RequestId}", requestId);
            return false;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 根据消息ID获取申请
    /// </summary>
    /// <param name="messageId">关联的消息ID</param>
    /// <returns>关联的申请，不存在时返回null</returns>
    public async Task<DepositWithdrawRequest?> GetRequestByMessageIdAsync(string messageId)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"根据消息ID获取申请: {MessageId}", messageId);
            return await DbContext.DepositWithdrawRequests
                .Where(r => r.MessageId == messageId)
                .FirstAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据消息ID获取申请失败: {MessageId}", messageId);
            return null;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 获取申请统计信息
    /// </summary>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>申请统计信息</returns>
    public async Task<(int PendingCount, int ApprovedCount, int RejectedCount, decimal TotalAmount)> GetRequestStatisticsAsync(DateTime startDate, DateTime endDate)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"获取申请统计信息: {StartDate} - {EndDate}", startDate, endDate);

            var requests = await DbContext.DepositWithdrawRequests
                .Where(r => r.CreatedAt >= startDate && r.CreatedAt <= endDate)
                .ToListAsync();

            var pendingCount = requests.Count(r => r.Status == CommandGuard.Enums.EnumDepositWithdrawRequestStatus.Pending);
            var approvedCount = requests.Count(r => r.Status == CommandGuard.Enums.EnumDepositWithdrawRequestStatus.Approved);
            var rejectedCount = requests.Count(r => r.Status == CommandGuard.Enums.EnumDepositWithdrawRequestStatus.Rejected);
            var totalAmount = requests.Sum(r => r.Amount);

            return (pendingCount, approvedCount, rejectedCount, totalAmount);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"获取申请统计信息失败");
            return (0, 0, 0, 0);
        }
        finally
        {
            Semaphore.Release();
        }
    }
