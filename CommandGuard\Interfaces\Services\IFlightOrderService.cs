using CommandGuard.Models;

namespace CommandGuard.Interfaces.Services;

/// <summary>
/// 飞单订单服务接口，提供飞单订单相关的业务逻辑
/// </summary>
public interface IFlightOrderService
{
    /// <summary>
    /// 创建飞单订单
    /// </summary>
    /// <param name="flightOrder">飞单订单信息</param>
    /// <returns>创建的飞单订单对象</returns>
    Task<FlightOrder> CreateFlightOrderAsync(FlightOrder flightOrder);

    /// <summary>
    /// 根据期号获取飞单订单列表
    /// </summary>
    /// <param name="issue">投注期号</param>
    /// <returns>该期号的飞单订单列表</returns>
    Task<IEnumerable<FlightOrder>> GetFlightOrdersByIssueAsync(string issue);

    /// <summary>
    /// 获取待处理的飞单订单列表
    /// </summary>
    /// <returns>待处理的飞单订单列表</returns>
    Task<IEnumerable<FlightOrder>> GetPendingFlightOrdersAsync();

    /// <summary>
    /// 处理飞单订单
    /// </summary>
    /// <param name="flightOrderId">飞单订单ID</param>
    /// <returns>处理成功返回true，否则返回false</returns>
    Task<bool> ProcessFlightOrderAsync(long flightOrderId);

    /// <summary>
    /// 批量处理飞单订单
    /// </summary>
    /// <param name="issue">期号</param>
    /// <returns>处理成功的订单数量</returns>
    Task<int> BatchProcessFlightOrdersAsync(string issue);

    /// <summary>
    /// 更新飞单状态
    /// </summary>
    /// <param name="flightOrderId">飞单订单ID</param>
    /// <param name="status">新状态</param>
    /// <returns>更新成功返回true，否则返回false</returns>
    Task<bool> UpdateFlightOrderStatusAsync(long flightOrderId, CommandGuard.Enums.EnumFlightOrderStatus status);

    /// <summary>
    /// 取消飞单订单
    /// </summary>
    /// <param name="flightOrderId">飞单订单ID</param>
    /// <returns>取消成功返回true，否则返回false</returns>
    Task<bool> CancelFlightOrderAsync(long flightOrderId);

    /// <summary>
    /// 获取飞单统计信息
    /// </summary>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>飞单统计信息</returns>
    Task<(int PendingCount, int ProcessingCount, int SuccessCount, int FailedCount, decimal TotalAmount)> GetFlightOrderStatisticsAsync(DateTime startDate, DateTime endDate);

    /// <summary>
    /// 验证飞单订单
    /// </summary>
    /// <param name="flightOrder">飞单订单</param>
    /// <returns>验证结果</returns>
    Task<(bool IsValid, string ErrorMessage)> ValidateFlightOrderAsync(FlightOrder flightOrder);

    /// <summary>
    /// 删除飞单订单
    /// </summary>
    /// <param name="flightOrderId">飞单订单ID</param>
    /// <returns>删除成功返回true，否则返回false</returns>
    Task<bool> DeleteFlightOrderAsync(long flightOrderId);
}
