using CommandGuard.Data;
using CommandGuard.Interfaces.Repositories;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Repositories;

/// <summary>
/// 成员仓储实现类，提供成员相关的数据访问方法
/// </summary>
public class MemberRepository(AppDbContext dbContext, ILogger<MemberRepository> logger)
    : BaseRepository<Member>(dbContext, logger), IMemberRepository
{
    /// <summary>
    /// 根据账号获取成员信息
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <returns>成员信息，不存在时返回null</returns>
    public async Task<Member?> GetByAccountAsync(string account)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"根据账号获取成员信息: {Account}", account);
            return await DbContext.Members.Where(m => m.Account == account).FirstAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据账号获取成员信息失败: {Account}", account);
            return null;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 检查账号是否已存在
    /// </summary>
    /// <param name="account">要检查的账号</param>
    /// <returns>存在返回true，否则返回false</returns>
    public async Task<bool> AccountExistsAsync(string account)
    {
        await Semaphore.WaitAsync();
        try
        {
            return await DbContext.Members.Where(m => m.Account == account).AnyAsync();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 更新成员余额
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <param name="newBalance">新余额</param>
    /// <returns>更新成功返回true，否则返回false</returns>
    public async Task<bool> UpdateBalanceAsync(string account, decimal newBalance)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"更新成员余额: {Account}, 新余额: {NewBalance}", account, newBalance);
            var affectedRows = await DbContext.UpdateMember
                .Set(m => m.Balance, newBalance)
                .Set(m => m.UpdatedAt, DateTime.Now)
                .Where(m => m.Account == account)
                .ExecuteAffrowsAsync();
            return affectedRows > 0;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"更新成员余额失败: {Account}", account);
            return false;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 根据代理名称获取下级成员列表
    /// </summary>
    /// <param name="agentName">代理名称</param>
    /// <returns>下级成员列表</returns>
    public async Task<IEnumerable<Member>> GetMembersByAgentAsync(string agentName)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"根据代理名称获取下级成员: {AgentName}", agentName);
            return await DbContext.Members.Where(m => m.AgentName == agentName).ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据代理名称获取下级成员失败: {AgentName}", agentName);
            return new List<Member>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 获取余额大于指定金额的成员列表
    /// </summary>
    /// <param name="minBalance">最小余额</param>
    /// <returns>符合条件的成员列表</returns>
    public async Task<IEnumerable<Member>> GetMembersByMinBalanceAsync(decimal minBalance)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"获取余额大于指定金额的成员: {MinBalance}", minBalance);
            return await DbContext.Members.Where(m => m.Balance >= minBalance).OrderByDescending(m => m.Balance).ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"获取余额大于指定金额的成员失败: {MinBalance}", minBalance);
            return new List<Member>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 获取指定时间范围内注册的成员列表
    /// </summary>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>符合条件的成员列表</returns>
    public async Task<IEnumerable<Member>> GetMembersByDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"获取指定时间范围内注册的成员: {StartDate} - {EndDate}", startDate, endDate);
            return await DbContext.Members
                .Where(m => m.CreatedAt >= startDate && m.CreatedAt <= endDate)
                .OrderByDescending(m => m.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"获取指定时间范围内注册的成员失败: {StartDate} - {EndDate}", startDate, endDate);
            return new List<Member>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 批量更新成员信息
    /// </summary>
    /// <param name="members">要更新的成员列表</param>
    /// <returns>更新成功的数量</returns>
    public async Task<int> BatchUpdateAsync(IEnumerable<Member> members)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"批量更新成员信息，数量: {Count}", members.Count());
            var memberList = members.ToList();
            var successCount = 0;

            foreach (var member in memberList)
            {
                try
                {
                    SetTimestamps(member, isUpdate: true);
                    await DbContext.UpdateMember.SetSource(member).ExecuteAffrowsAsync();
                    successCount++;
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, @"批量更新成员失败: {Account}", member.Account);
                }
            }

            Logger.LogInformation(@"批量更新成员完成，成功: {SuccessCount}/{TotalCount}", successCount, memberList.Count);
            return successCount;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"批量更新成员信息失败");
            return 0;
        }
        finally
        {
            Semaphore.Release();
        }
    }