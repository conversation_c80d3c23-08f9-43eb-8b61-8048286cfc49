using CommandGuard.Models;

namespace CommandGuard.Interfaces.Services;

/// <summary>
/// 成员服务接口，提供成员相关的业务逻辑
/// </summary>
public interface IMemberService
{
    /// <summary>
    /// 创建新成员
    /// </summary>
    /// <param name="member">成员信息</param>
    /// <returns>创建的成员对象</returns>
    Task<Member> CreateMemberAsync(Member member);

    /// <summary>
    /// 根据账号获取成员信息
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <returns>成员信息，不存在时返回null</returns>
    Task<Member?> GetMemberByAccountAsync(string account);

    /// <summary>
    /// 更新成员信息
    /// </summary>
    /// <param name="member">成员信息</param>
    /// <returns>更新后的成员对象</returns>
    Task<Member> UpdateMemberAsync(Member member);

    /// <summary>
    /// 检查账号是否已存在
    /// </summary>
    /// <param name="account">要检查的账号</param>
    /// <returns>存在返回true，否则返回false</returns>
    Task<bool> IsAccountExistsAsync(string account);

    /// <summary>
    /// 更新成员余额
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <param name="amount">变更金额（正数为增加，负数为减少）</param>
    /// <param name="financialType">财务类型</param>
    /// <param name="referenceId">关联业务ID</param>
    /// <returns>更新后的余额</returns>
    Task<decimal> UpdateMemberBalanceAsync(string account, decimal amount, CommandGuard.Enums.EnumFinancialType financialType, long referenceId = 0);

    /// <summary>
    /// 获取所有成员列表
    /// </summary>
    /// <returns>成员列表</returns>
    Task<IEnumerable<Member>> GetAllMembersAsync();

    /// <summary>
    /// 根据代理名称获取下级成员列表
    /// </summary>
    /// <param name="agentName">代理名称</param>
    /// <returns>下级成员列表</returns>
    Task<IEnumerable<Member>> GetMembersByAgentAsync(string agentName);

    /// <summary>
    /// 删除成员
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <returns>删除成功返回true，否则返回false</returns>
    Task<bool> DeleteMemberAsync(string account);

    /// <summary>
    /// 获取成员统计信息
    /// </summary>
    /// <returns>成员统计信息</returns>
    Task<(int TotalMembers, decimal TotalBalance, int ActiveMembers)> GetMemberStatisticsAsync();

    /// <summary>
    /// 验证成员余额是否足够
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <param name="amount">需要的金额</param>
    /// <returns>余额足够返回true，否则返回false</returns>
    Task<bool> ValidateBalanceAsync(string account, decimal amount);

    /// <summary>
    /// 搜索成员
    /// </summary>
    /// <param name="keyword">搜索关键字（账号或昵称）</param>
    /// <returns>符合条件的成员列表</returns>
    Task<IEnumerable<Member>> SearchMembersAsync(string keyword);
}
