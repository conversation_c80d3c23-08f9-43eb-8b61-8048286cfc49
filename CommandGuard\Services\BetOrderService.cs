using CommandGuard.Interfaces.Repositories;
using CommandGuard.Interfaces.Services;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services;

/// <summary>
/// 投注订单服务实现类，提供投注订单相关的业务逻辑
/// </summary>
public class BetOrderService(
    ILogger<BetOrderService> logger,
    IBetOrderRepository betOrderRepository,
    IMemberRepository memberRepository,
    IMemberService memberService,
    IFinancialService financialService
) : IBetOrderService
{
    /// <summary>
    /// 创建投注订单
    /// </summary>
    /// <param name="betOrder">投注订单信息</param>
    /// <returns>创建的投注订单对象</returns>
    public async Task<BetOrder> CreateBetOrderAsync(BetOrder betOrder)
    {
        try
        {
            logger.LogInformation(@"创建投注订单: {Account}, {Amount}", betOrder.Account, betOrder.Amount);

            // 验证投注订单
            var (isValid, errorMessage) = await ValidateBetOrderAsync(betOrder);
            if (!isValid)
            {
                throw new InvalidOperationException(errorMessage);
            }

            // 验证成员余额
            if (!await memberService.ValidateBalanceAsync(betOrder.Account, betOrder.Amount))
            {
                throw new InvalidOperationException(@"余额不足");
            }

            // 生成订单号
            betOrder.OrderNumber = await GenerateOrderNumberAsync();
            betOrder.Status = CommandGuard.Enums.EnumBetOrderStatus.Confirmed;
            betOrder.CreatedAt = DateTime.Now;
            betOrder.UpdatedAt = DateTime.Now;

            // 扣除投注金额
            await memberService.UpdateMemberBalanceAsync(
                betOrder.Account,
                -betOrder.Amount,
                CommandGuard.Enums.EnumFinancialType.Bet,
                0
            );

            var createdOrder = await betOrderRepository.AddAsync(betOrder);
            logger.LogInformation(@"投注订单创建成功: {OrderNumber}", createdOrder.OrderNumber);

            return createdOrder;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"创建投注订单失败: {Account}", betOrder.Account);
            throw;
        }
    }

    /// <summary>
    /// 根据订单号获取投注订单
    /// </summary>
    /// <param name="orderNumber">订单号</param>
    /// <returns>投注订单，不存在时返回null</returns>
    public async Task<BetOrder?> GetBetOrderByNumberAsync(string orderNumber)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(orderNumber))
            {
                throw new ArgumentException(@"订单号不能为空");
            }

            return await betOrderRepository.GetByOrderNumberAsync(orderNumber);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"根据订单号获取投注订单失败: {OrderNumber}", orderNumber);
            throw;
        }
    }

    /// <summary>
    /// 根据账号获取投注订单列表
    /// </summary>
    /// <param name="account">投注用户账号</param>
    /// <returns>该账号的投注订单列表</returns>
    public async Task<IEnumerable<BetOrder>> GetBetOrdersByAccountAsync(string account)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(account))
            {
                throw new ArgumentException(@"账号不能为空");
            }

            return await betOrderRepository.GetOrdersByAccountAsync(account);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"根据账号获取投注订单列表失败: {Account}", account);
            throw;
        }
    }

    /// <summary>
    /// 根据期号获取投注订单列表
    /// </summary>
    /// <param name="issue">投注期号</param>
    /// <returns>该期号的投注订单列表</returns>
    public async Task<IEnumerable<BetOrder>> GetBetOrdersByIssueAsync(string issue)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(issue))
            {
                throw new ArgumentException(@"期号不能为空");
            }

            return await betOrderRepository.GetOrdersByIssueAsync(issue);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"根据期号获取投注订单列表失败: {Issue}", issue);
            throw;
        }
    }

    /// <summary>
    /// 结算投注订单
    /// </summary>
    /// <param name="issue">期号</param>
    /// <param name="drawResult">开奖结果</param>
    /// <returns>结算的订单数量</returns>
    public async Task<int> SettleBetOrdersAsync(string issue, string drawResult)
    {
        try
        {
            logger.LogInformation(@"结算投注订单: {Issue}, 开奖结果: {DrawResult}", issue, drawResult);

            var orders = await betOrderRepository.GetOrdersByIssueAsync(issue);
            var confirmedOrders = orders.Where(o => o.Status == CommandGuard.Enums.EnumBetOrderStatus.Confirmed).ToList();

            var settledCount = 0;
            foreach (var order in confirmedOrders)
            {
                try
                {
                    var (isWin, settledAmount) = CalculateSettlement(order, drawResult);
                    var status = isWin ? CommandGuard.Enums.EnumBetOrderStatus.Won : CommandGuard.Enums.EnumBetOrderStatus.Lost;

                    // 更新订单状态和结算金额
                    await betOrderRepository.SettleOrderAsync(order.Id, settledAmount, status);

                    // 如果中奖，给用户加钱
                    if (isWin && settledAmount > 0)
                    {
                        await memberService.UpdateMemberBalanceAsync(
                            order.Account,
                            settledAmount,
                            CommandGuard.Enums.EnumFinancialType.Win,
                            order.Id
                        );
                    }

                    settledCount++;
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, @"结算订单失败: {OrderNumber}", order.OrderNumber);
                }
            }

            logger.LogInformation(@"期号 {Issue} 结算完成，共结算 {SettledCount} 个订单", issue, settledCount);
            return settledCount;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"结算投注订单失败: {Issue}", issue);
            throw;
        }
    }

    /// <summary>
    /// 取消投注订单
    /// </summary>
    /// <param name="orderNumber">订单号</param>
    /// <returns>取消成功返回true，否则返回false</returns>
    public async Task<bool> CancelBetOrderAsync(string orderNumber)
    {
        try
        {
            logger.LogInformation(@"取消投注订单: {OrderNumber}", orderNumber);

            var order = await betOrderRepository.GetByOrderNumberAsync(orderNumber);
            if (order == null)
            {
                throw new InvalidOperationException(@$"订单 {orderNumber} 不存在");
            }

            if (order.Status != CommandGuard.Enums.EnumBetOrderStatus.Confirmed)
            {
                throw new InvalidOperationException(@$"订单 {orderNumber} 状态不允许取消");
            }

            // 更新订单状态
            var success = await betOrderRepository.UpdateOrderStatusAsync(order.Id, CommandGuard.Enums.EnumBetOrderStatus.Cancelled);
            if (success)
            {
                // 退还投注金额
                await memberService.UpdateMemberBalanceAsync(
                    order.Account,
                    order.Amount,
                    CommandGuard.Enums.EnumFinancialType.Refund,
                    order.Id
                );
            }

            return success;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"取消投注订单失败: {OrderNumber}", orderNumber);
            throw;
        }
    }

    /// <summary>
    /// 验证投注订单
    /// </summary>
    /// <param name="betOrder">投注订单</param>
    /// <returns>验证结果</returns>
    public async Task<(bool IsValid, string ErrorMessage)> ValidateBetOrderAsync(BetOrder betOrder)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(betOrder.Account))
            {
                return (false, @"投注账号不能为空");
            }

            if (string.IsNullOrWhiteSpace(betOrder.Issue))
            {
                return (false, @"投注期号不能为空");
            }

            if (betOrder.Amount <= 0)
            {
                return (false, @"投注金额必须大于0");
            }

            if (betOrder.Amount < 1)
            {
                return (false, @"投注金额不能小于1元");
            }

            if (betOrder.Amount > 10000)
            {
                return (false, @"投注金额不能超过10000元");
            }

            // 检查成员是否存在
            var member = await memberService.GetMemberByAccountAsync(betOrder.Account);
            if (member == null)
            {
                return (false, @$"成员 {betOrder.Account} 不存在");
            }

            // 检查期号是否可以投注
            if (!await CanBetOnIssueAsync(betOrder.Issue))
            {
                return (false, @$"期号 {betOrder.Issue} 不能投注");
            }

            return (true, string.Empty);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"验证投注订单失败");
            return (false, @"验证失败");
        }
    }

    /// <summary>
    /// 生成订单号
    /// </summary>
    /// <returns>唯一的订单号</returns>
    public async Task<string> GenerateOrderNumberAsync()
    {
        await Task.CompletedTask;
        return @$"BO{DateTime.Now:yyyyMMddHHmmss}{Random.Shared.Next(1000, 9999)}";
    }

    /// <summary>
    /// 检查期号是否可以投注
    /// </summary>
    /// <param name="issue">期号</param>
    /// <returns>可以投注返回true，否则返回false</returns>
    public async Task<bool> CanBetOnIssueAsync(string issue)
    {
        await Task.CompletedTask;
        // 这里可以添加具体的期号验证逻辑，比如检查期号是否已开奖、是否在投注时间内等
        return !string.IsNullOrWhiteSpace(issue);
    }

    /// <summary>
    /// 处理回水
    /// </summary>
    /// <param name="orderNumber">订单号</param>
    /// <returns>处理成功返回true，否则返回false</returns>
    public async Task<bool> ProcessRebateAsync(string orderNumber)
    {
        try
        {
            logger.LogInformation(@"处理回水: {OrderNumber}", orderNumber);

            var order = await betOrderRepository.GetByOrderNumberAsync(orderNumber);
            if (order == null)
            {
                throw new InvalidOperationException(@$"订单 {orderNumber} 不存在");
            }

            // 获取成员信息计算回水
            var member = await memberService.GetMemberByAccountAsync(order.Account);
            if (member == null)
            {
                throw new InvalidOperationException(@$"成员 {order.Account} 不存在");
            }

            var rebateAmount = order.Amount * (member.RebatePercentage / 100);
            if (rebateAmount <= 0)
            {
                return true; // 没有回水
            }

            // 更新订单回水信息
            var success = await betOrderRepository.ProcessRebateAsync(order.Id, rebateAmount);
            if (success)
            {
                // 给用户加回水
                await memberService.UpdateMemberBalanceAsync(
                    order.Account,
                    rebateAmount,
                    CommandGuard.Enums.EnumFinancialType.Rebate,
                    order.Id
                );
            }

            return success;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"处理回水失败: {OrderNumber}", orderNumber);
            throw;
        }
    }

    /// <summary>
    /// 批量处理回水
    /// </summary>
    /// <param name="issue">期号</param>
    /// <returns>处理成功的订单数量</returns>
    public async Task<int> BatchProcessRebateAsync(string issue)
    {
        try
        {
            logger.LogInformation(@"批量处理回水: {Issue}", issue);

            var orders = await betOrderRepository.GetOrdersByIssueAsync(issue);
            var eligibleOrders = orders.Where(o =>
                o.Status == CommandGuard.Enums.EnumBetOrderStatus.Won ||
                o.Status == CommandGuard.Enums.EnumBetOrderStatus.Lost
            ).ToList();

            var processedCount = 0;
            foreach (var order in eligibleOrders)
            {
                try
                {
                    if (await ProcessRebateAsync(order.OrderNumber))
                    {
                        processedCount++;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, @"批量处理回水失败: {OrderNumber}", order.OrderNumber);
                }
            }

            logger.LogInformation(@"期号 {Issue} 回水处理完成，共处理 {ProcessedCount} 个订单", issue, processedCount);
            return processedCount;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"批量处理回水失败: {Issue}", issue);
            throw;
        }
    }

    /// <summary>
    /// 获取投注统计信息
    /// </summary>
    /// <param name="account">账号（可选）</param>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>投注统计信息</returns>
    public async Task<(decimal TotalAmount, decimal TotalWin, int TotalOrders, decimal WinRate)> GetBetStatisticsAsync(string? account, DateTime startDate, DateTime endDate)
    {
        try
        {
            logger.LogInformation(@"获取投注统计信息: {Account}, {StartDate} - {EndDate}", account ?? "全部", startDate, endDate);

            if (!string.IsNullOrWhiteSpace(account))
            {
                var (totalAmount, totalWin, totalOrders) = await betOrderRepository.GetBetStatisticsByAccountAsync(account, startDate, endDate);
                var winRate = totalOrders > 0 ? (totalWin / totalAmount) * 100 : 0;
                return (totalAmount, totalWin, totalOrders, winRate);
            }
            else
            {
                var orders = await betOrderRepository.GetOrdersByDateRangeAsync(startDate, endDate);
                var orderList = orders.ToList();

                var totalAmount = orderList.Sum(o => o.Amount);
                var totalWin = orderList.Where(o => o.SettledAmount.HasValue).Sum(o => o.SettledAmount.Value);
                var totalOrders = orderList.Count;
                var winRate = totalAmount > 0 ? (totalWin / totalAmount) * 100 : 0;

                return (totalAmount, totalWin, totalOrders, winRate);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取投注统计信息失败");
            throw;
        }
    }

    /// <summary>
    /// 计算结算金额
    /// </summary>
    /// <param name="order">投注订单</param>
    /// <param name="drawResult">开奖结果</param>
    /// <returns>是否中奖和结算金额</returns>
    private static (bool IsWin, decimal SettledAmount) CalculateSettlement(BetOrder order, string drawResult)
    {
        // 这里应该根据具体的游戏规则来计算是否中奖和中奖金额
        // 简化实现：随机决定是否中奖
        var isWin = Random.Shared.NextDouble() < 0.4; // 40%中奖率
        var settledAmount = isWin ? order.Amount * (decimal)order.Odds : 0;

        return (isWin, settledAmount);
    }