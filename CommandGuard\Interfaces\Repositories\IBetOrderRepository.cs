using CommandGuard.Models;

namespace CommandGuard.Interfaces.Repositories;

/// <summary>
/// 投注订单仓储接口，提供投注订单相关的数据访问方法
/// </summary>
public interface IBetOrderRepository : IRepository<BetOrder>
{
    /// <summary>
    /// 根据订单号获取投注订单
    /// </summary>
    /// <param name="orderNumber">订单号</param>
    /// <returns>投注订单，不存在时返回null</returns>
    Task<BetOrder?> GetByOrderNumberAsync(string orderNumber);

    /// <summary>
    /// 根据账号获取投注订单列表
    /// </summary>
    /// <param name="account">投注用户账号</param>
    /// <returns>该账号的投注订单列表</returns>
    Task<IEnumerable<BetOrder>> GetOrdersByAccountAsync(string account);

    /// <summary>
    /// 根据期号获取投注订单列表
    /// </summary>
    /// <param name="issue">投注期号</param>
    /// <returns>该期号的投注订单列表</returns>
    Task<IEnumerable<BetOrder>> GetOrdersByIssueAsync(string issue);

    /// <summary>
    /// 根据状态获取投注订单列表
    /// </summary>
    /// <param name="status">订单状态</param>
    /// <returns>指定状态的投注订单列表</returns>
    Task<IEnumerable<BetOrder>> GetOrdersByStatusAsync(CommandGuard.Enums.EnumBetOrderStatus status);

    /// <summary>
    /// 获取指定时间范围内的投注订单
    /// </summary>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>符合条件的投注订单列表</returns>
    Task<IEnumerable<BetOrder>> GetOrdersByDateRangeAsync(DateTime startDate, DateTime endDate);

    /// <summary>
    /// 根据账号和期号获取投注订单
    /// </summary>
    /// <param name="account">投注用户账号</param>
    /// <param name="issue">投注期号</param>
    /// <returns>符合条件的投注订单列表</returns>
    Task<IEnumerable<BetOrder>> GetOrdersByAccountAndIssueAsync(string account, string issue);

    /// <summary>
    /// 更新订单状态
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <param name="status">新状态</param>
    /// <returns>更新成功返回true，否则返回false</returns>
    Task<bool> UpdateOrderStatusAsync(long orderId, CommandGuard.Enums.EnumBetOrderStatus status);

    /// <summary>
    /// 结算订单
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <param name="settledAmount">结算金额</param>
    /// <param name="status">结算状态</param>
    /// <returns>更新成功返回true，否则返回false</returns>
    Task<bool> SettleOrderAsync(long orderId, decimal settledAmount, CommandGuard.Enums.EnumBetOrderStatus status);

    /// <summary>
    /// 发放回水
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <param name="rebateAmount">回水金额</param>
    /// <returns>更新成功返回true，否则返回false</returns>
    Task<bool> ProcessRebateAsync(long orderId, decimal rebateAmount);

    /// <summary>
    /// 获取账号的投注统计信息
    /// </summary>
    /// <param name="account">投注用户账号</param>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>投注统计信息</returns>
    Task<(decimal TotalAmount, decimal TotalWin, int TotalOrders)> GetBetStatisticsByAccountAsync(string account, DateTime startDate, DateTime endDate);
}
