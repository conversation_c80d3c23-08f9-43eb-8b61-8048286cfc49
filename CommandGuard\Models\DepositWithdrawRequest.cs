﻿using System.ComponentModel.DataAnnotations;
using CommandGuard.Enums;
using FreeSql.DataAnnotations;

namespace CommandGuard.Models;

/// <summary>
/// 存取款申请实体类，对应数据库中的DepositWithdrawRequests表
///
/// 功能说明：
/// - 管理系统中的存款和取款申请
/// - 支持申请的创建、审核和状态管理
/// - 提供完整的申请流程追踪
/// - 支持金额管理和审核记录
/// - 提供完整的审计追踪（创建时间、审核时间）
///
/// 业务规则：
/// - 每个申请必须关联一个有效的成员账号
/// - 申请金额必须大于0
/// - 申请状态默认为待审核(Pending)
/// - 审核后状态不可逆转
/// - 支持存款(Deposit)和取款(Withdraw)两种类型
/// - 审核时间在状态变更时自动更新
///
/// 索引设计：
/// - idx_account: 按账号查询申请
/// - idx_type: 按申请类型查询（存款/取款）
/// - idx_status: 按状态查询（待审核/已通过/已拒绝）
/// - idx_createdat: 按申请时间查询
/// - idx_account_status: 复合索引：账号+状态
/// - idx_type_status: 复合索引：类型+状态
/// </summary>
[Table(Name = "DepositWithdrawRequests")]
[Index("idx_account", "Account", false)]                    // 按账号查询
[Index("idx_type", "Type", false)]                          // 按类型查询
[Index("idx_status", "Status", false)]                      // 按状态查询
[Index("idx_createdat", "CreatedAt desc", false)]           // 按时间查询
[Index("idx_account_status", "Account,Status", false)]      // 复合索引：账号+状态
[Index("idx_type_status", "Type,Status", false)]            // 复合索引：类型+状态
public class DepositWithdrawRequest
{
    /// <summary>
    /// 申请唯一标识符
    /// 数据库主键，自动递增，用于唯一标识每个存取款申请记录
    /// 数据类型：long - 64位整数，支持海量申请记录
    /// </summary>
    [Column(IsIdentity = true, IsPrimary = true)]
    public long Id { get; set; }

    /// <summary>
    /// 申请人账号
    /// 提交存取款申请的成员账号，必须是系统中存在的有效成员账号
    /// 约束：最大长度100字符，不能为空，必须关联有效的成员账号
    /// 用途：申请人身份识别、权限验证、资金操作授权等
    /// </summary>
    [Required(ErrorMessage = @"申请人账号不能为空")]
    [StringLength(100, ErrorMessage = @"申请人账号长度不能超过100个字符")]
    [Column(StringLength = 100, IsNullable = false)]
    public string Account { get; set; } = string.Empty;

    /// <summary>
    /// 申请类型
    /// 标识申请的操作类型：存款(Deposit)或取款(Withdraw)
    /// 枚举类型：EnumDepositWithdrawRequestType
    /// 约束：不能为空，必须是有效的枚举值
    /// 用途：业务流程区分、权限控制、操作验证等
    /// </summary>
    [Required(ErrorMessage = @"申请类型不能为空")]
    [Column(IsNullable = false)]
    public EnumDepositWithdrawRequestType RequestType { get; set; }

    /// <summary>
    /// 申请金额
    /// 存款或取款的具体金额，支持精确的货币计算
    /// 数据类型：decimal(18,2) - 精度18位，小数点后2位
    /// 约束：必须大于0，不能为空
    /// 范围：0.01 到 ****************.99
    /// 用途：资金操作、余额计算、财务记录等
    /// </summary>
    [Required(ErrorMessage = @"申请金额不能为空")]
    [Range(0.01, ****************.99, ErrorMessage = @"申请金额必须大于0.01")]
    [Column(Precision = 18, Scale = 2, IsNullable = false)]
    public decimal Amount { get; set; }

    /// <summary>
    /// 关联的消息ID
    /// 与申请相关的消息记录标识，用于关联申请和通知消息
    /// 约束：最大长度50字符，可为空（某些申请可能不需要关联消息）
    /// 用途：消息关联、通知追踪、审核记录等
    /// </summary>
    [StringLength(50, ErrorMessage = @"关联消息ID长度不能超过50个字符")]
    [Column(StringLength = 50, IsNullable = true)]
    public string MessageId { get; set; } = string.Empty;

    /// <summary>
    /// 申请状态
    /// 标识申请的当前处理状态：待审核(Pending)、已批准(Approved)、已拒绝(Rejected)
    /// 枚举类型：EnumDepositWithdrawRequestStatus
    /// 约束：不能为空，默认值为Pending（待审核）
    /// 业务：状态变更后不可逆转，需要完整的审核流程
    /// 用途：流程控制、权限验证、状态追踪等
    /// </summary>
    [Required(ErrorMessage = @"申请状态不能为空")]
    [Column(IsNullable = false)]
    public EnumDepositWithdrawRequestStatus Status { get; set; } = EnumDepositWithdrawRequestStatus.Pending;

    /// <summary>
    /// 申请创建时间
    /// 申请首次提交的时间戳，用于申请排序和审计追踪
    /// 约束：不可为空，自动设置为当前时间
    /// 用途：申请排序、时间筛选、审核时效、数据分析等
    /// </summary>
    [Required(ErrorMessage = @"创建时间不能为空")]
    [Column(IsNullable = false, ServerTime = DateTimeKind.Local)]
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 申请审核时间
    /// 申请状态最后一次变更的时间戳（审核通过或拒绝的时间）
    /// 约束：可为空，待审核状态时为null，审核后自动设置
    /// 用途：审核追踪、处理时效、审核员绩效、合规记录等
    /// </summary>
    [Column(IsNullable = true, ServerTime = DateTimeKind.Local)]
    public DateTime? UpdatedAt { get; set; }
}