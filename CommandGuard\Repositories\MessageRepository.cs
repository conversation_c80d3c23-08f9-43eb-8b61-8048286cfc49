using CommandGuard.Data;
using CommandGuard.Interfaces.Repositories;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Repositories;

/// <summary>
/// 消息仓储实现类，提供消息相关的数据访问方法
/// </summary>
public class MessageRepository(AppDbContext dbContext, ILogger<MessageRepository> logger)
    : BaseRepository<Models.Message>(dbContext, logger), IMessageRepository
{
    /// <summary>
    /// 获取所有未读消息
    /// </summary>
    /// <returns>未读消息列表</returns>
    public async Task<IEnumerable<Models.Message>> GetUnreadMessagesAsync()
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"获取所有未读消息");
            return await DbContext.Messages
                .Where(m => !m.IsRead)
                .OrderBy(m => m.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"获取所有未读消息失败");
            return new List<Models.Message>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 根据账号获取消息列表
    /// </summary>
    /// <param name="account">发送者账号</param>
    /// <returns>该账号的消息列表</returns>
    public async Task<IEnumerable<Models.Message>> GetMessagesByAccountAsync(string account)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"根据账号获取消息列表: {Account}", account);
            return await DbContext.Messages
                .Where(m => m.Account == account)
                .OrderByDescending(m => m.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据账号获取消息列表失败: {Account}", account);
            return new List<Models.Message>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 根据账号获取未读消息列表
    /// </summary>
    /// <param name="account">发送者账号</param>
    /// <returns>该账号的未读消息列表</returns>
    public async Task<IEnumerable<Models.Message>> GetUnreadMessagesByAccountAsync(string account)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"根据账号获取未读消息列表: {Account}", account);
            return await DbContext.Messages
                .Where(m => m.Account == account && !m.IsRead)
                .OrderBy(m => m.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据账号获取未读消息列表失败: {Account}", account);
            return new List<Models.Message>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 标记消息为已读
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <returns>更新成功返回true，否则返回false</returns>
    public async Task<bool> MarkAsReadAsync(long messageId)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"标记消息为已读: {MessageId}", messageId);
            var affectedRows = await DbContext.UpdateMessage
                .Set(m => m.IsRead, true)
                .Set(m => m.UpdatedAt, DateTime.Now)
                .Where(m => m.Id == messageId)
                .ExecuteAffrowsAsync();
            return affectedRows > 0;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"标记消息为已读失败: {MessageId}", messageId);
            return false;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 批量标记消息为已读
    /// </summary>
    /// <param name="messageIds">消息ID列表</param>
    /// <returns>更新成功的数量</returns>
    public async Task<int> BatchMarkAsReadAsync(IEnumerable<long> messageIds)
    {
        await Semaphore.WaitAsync();
        try
        {
            var idList = messageIds.ToList();
            Logger.LogInformation(@"批量标记消息为已读，数量: {Count}", idList.Count);

            var affectedRows = await DbContext.UpdateMessage
                .Set(m => m.IsRead, true)
                .Set(m => m.UpdatedAt, DateTime.Now)
                .Where(m => idList.Contains(m.Id))
                .ExecuteAffrowsAsync();

            return affectedRows;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"批量标记消息为已读失败");
            return 0;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 获取指定时间范围内的消息
    /// </summary>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>符合条件的消息列表</returns>
    public async Task<IEnumerable<Models.Message>> GetMessagesByDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"获取指定时间范围内的消息: {StartDate} - {EndDate}", startDate, endDate);
            return await DbContext.Messages
                .Where(m => m.CreatedAt >= startDate && m.CreatedAt <= endDate)
                .OrderByDescending(m => m.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"获取指定时间范围内的消息失败: {StartDate} - {EndDate}", startDate, endDate);
            return new List<Models.Message>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 根据内容关键字搜索消息
    /// </summary>
    /// <param name="keyword">搜索关键字</param>
    /// <returns>包含关键字的消息列表</returns>
    public async Task<IEnumerable<Models.Message>> SearchMessagesByContentAsync(string keyword)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"根据内容关键字搜索消息: {Keyword}", keyword);
            return await DbContext.Messages
                .Where(m => m.Content.Contains(keyword))
                .OrderByDescending(m => m.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据内容关键字搜索消息失败: {Keyword}", keyword);
            return new List<Models.Message>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 获取未读消息数量
    /// </summary>
    /// <returns>未读消息数量</returns>
    public async Task<int> GetUnreadMessageCountAsync()
    {
        await Semaphore.WaitAsync();
        try
        {
            return (int)await DbContext.Messages.Where(m => !m.IsRead).CountAsync();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 根据账号获取未读消息数量
    /// </summary>
    /// <param name="account">发送者账号</param>
    /// <returns>该账号的未读消息数量</returns>
    public async Task<int> GetUnreadMessageCountByAccountAsync(string account)
    {
        await Semaphore.WaitAsync();
        try
        {
            return (int)await DbContext.Messages
                .Where(m => m.Account == account && !m.IsRead)
                .CountAsync();
        }
        finally
        {
            Semaphore.Release();
        }
    }