using CommandGuard.Data;
using CommandGuard.Interfaces.Repositories;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Repositories;

/// <summary>
/// 投注订单仓储实现类，提供投注订单相关的数据访问方法
/// </summary>
public class BetOrderRepository(AppDbContext dbContext, ILogger<BetOrderRepository> logger)
    : BaseRepository<BetOrder>(dbContext, logger), IBetOrderRepository
{
    /// <summary>
    /// 根据订单号获取投注订单
    /// </summary>
    /// <param name="orderNumber">订单号</param>
    /// <returns>投注订单，不存在时返回null</returns>
    public async Task<BetOrder?> GetByOrderNumberAsync(string orderNumber)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"根据订单号获取投注订单: {OrderNumber}", orderNumber);
            return await DbContext.BetOrders.Where(b => b.OrderNumber == orderNumber).FirstAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据订单号获取投注订单失败: {OrderNumber}", orderNumber);
            return null;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 根据账号获取投注订单列表
    /// </summary>
    /// <param name="account">投注用户账号</param>
    /// <returns>该账号的投注订单列表</returns>
    public async Task<IEnumerable<BetOrder>> GetOrdersByAccountAsync(string account)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"根据账号获取投注订单列表: {Account}", account);
            return await DbContext.BetOrders
                .Where(b => b.Account == account)
                .OrderByDescending(b => b.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据账号获取投注订单列表失败: {Account}", account);
            return new List<BetOrder>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 根据期号获取投注订单列表
    /// </summary>
    /// <param name="issue">投注期号</param>
    /// <returns>该期号的投注订单列表</returns>
    public async Task<IEnumerable<BetOrder>> GetOrdersByIssueAsync(string issue)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"根据期号获取投注订单列表: {Issue}", issue);
            return await DbContext.BetOrders
                .Where(b => b.Issue == issue)
                .OrderBy(b => b.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据期号获取投注订单列表失败: {Issue}", issue);
            return new List<BetOrder>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 根据状态获取投注订单列表
    /// </summary>
    /// <param name="status">订单状态</param>
    /// <returns>指定状态的投注订单列表</returns>
    public async Task<IEnumerable<BetOrder>> GetOrdersByStatusAsync(CommandGuard.Enums.EnumBetOrderStatus status)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"根据状态获取投注订单列表: {Status}", status);
            return await DbContext.BetOrders
                .Where(b => b.Status == status)
                .OrderByDescending(b => b.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据状态获取投注订单列表失败: {Status}", status);
            return new List<BetOrder>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 获取指定时间范围内的投注订单
    /// </summary>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>符合条件的投注订单列表</returns>
    public async Task<IEnumerable<BetOrder>> GetOrdersByDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"获取指定时间范围内的投注订单: {StartDate} - {EndDate}", startDate, endDate);
            return await DbContext.BetOrders
                .Where(b => b.CreatedAt >= startDate && b.CreatedAt <= endDate)
                .OrderByDescending(b => b.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"获取指定时间范围内的投注订单失败: {StartDate} - {EndDate}", startDate, endDate);
            return new List<BetOrder>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 根据账号和期号获取投注订单
    /// </summary>
    /// <param name="account">投注用户账号</param>
    /// <param name="issue">投注期号</param>
    /// <returns>符合条件的投注订单列表</returns>
    public async Task<IEnumerable<BetOrder>> GetOrdersByAccountAndIssueAsync(string account, string issue)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"根据账号和期号获取投注订单: {Account}, {Issue}", account, issue);
            return await DbContext.BetOrders
                .Where(b => b.Account == account && b.Issue == issue)
                .OrderBy(b => b.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据账号和期号获取投注订单失败: {Account}, {Issue}", account, issue);
            return new List<BetOrder>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 更新订单状态
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <param name="status">新状态</param>
    /// <returns>更新成功返回true，否则返回false</returns>
    public async Task<bool> UpdateOrderStatusAsync(long orderId, CommandGuard.Enums.EnumBetOrderStatus status)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"更新订单状态: {OrderId}, {Status}", orderId, status);
            var affectedRows = await DbContext.UpdateBetOrder
                .Set(b => b.Status, status)
                .Where(b => b.Id == orderId)
                .ExecuteAffrowsAsync();
            return affectedRows > 0;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"更新订单状态失败: {OrderId}", orderId);
            return false;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 结算订单
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <param name="settledAmount">结算金额</param>
    /// <param name="status">结算状态</param>
    /// <returns>更新成功返回true，否则返回false</returns>
    public async Task<bool> SettleOrderAsync(long orderId, decimal settledAmount, CommandGuard.Enums.EnumBetOrderStatus status)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"结算订单: {OrderId}, 结算金额: {SettledAmount}, 状态: {Status}", orderId, settledAmount, status);
            var affectedRows = await DbContext.UpdateBetOrder
                .Set(b => b.SettledAmount, settledAmount)
                .Set(b => b.Status, status)
                .Set(b => b.SettledTime, DateTime.Now)
                .Where(b => b.Id == orderId)
                .ExecuteAffrowsAsync();
            return affectedRows > 0;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"结算订单失败: {OrderId}", orderId);
            return false;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 发放回水
    /// </summary>
    /// <param name="orderId">订单ID</param>
    /// <param name="rebateAmount">回水金额</param>
    /// <returns>更新成功返回true，否则返回false</returns>
    public async Task<bool> ProcessRebateAsync(long orderId, decimal rebateAmount)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"发放回水: {OrderId}, 回水金额: {RebateAmount}", orderId, rebateAmount);
            var affectedRows = await DbContext.UpdateBetOrder
                .Set(b => b.RebateAmount, rebateAmount)
                .Set(b => b.RebateTime, DateTime.Now)
                .Where(b => b.Id == orderId)
                .ExecuteAffrowsAsync();
            return affectedRows > 0;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"发放回水失败: {OrderId}", orderId);
            return false;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 获取账号的投注统计信息
    /// </summary>
    /// <param name="account">投注用户账号</param>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>投注统计信息</returns>
    public async Task<(decimal TotalAmount, decimal TotalWin, int TotalOrders)> GetBetStatisticsByAccountAsync(string account, DateTime startDate, DateTime endDate)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"获取账号的投注统计信息: {Account}, {StartDate} - {EndDate}", account, startDate, endDate);

            var orders = await DbContext.BetOrders
                .Where(b => b.Account == account && b.CreatedAt >= startDate && b.CreatedAt <= endDate)
                .ToListAsync();

            var totalAmount = orders.Sum(b => b.Amount);
            var totalWin = orders.Where(b => b.SettledAmount.HasValue).Sum(b => b.SettledAmount.Value);
            var totalOrders = orders.Count;

            return (totalAmount, totalWin, totalOrders);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"获取账号的投注统计信息失败: {Account}", account);
            return (0, 0, 0);
        }
        finally
        {
            Semaphore.Release();
        }
    }
