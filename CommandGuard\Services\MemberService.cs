using CommandGuard.Interfaces.Repositories;
using CommandGuard.Interfaces.Services;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services;

/// <summary>
/// 成员服务实现类，提供成员相关的业务逻辑
/// </summary>
public class MemberService(
    ILogger<MemberService> logger,
    IMemberRepository memberRepository,
    IFinancialRepository financialRepository
) : IMemberService
{
    /// <summary>
    /// 创建新成员
    /// </summary>
    /// <param name="member">成员信息</param>
    /// <returns>创建的成员对象</returns>
    public async Task<Member> CreateMemberAsync(Member member)
    {
        try
        {
            logger.LogInformation(@"创建新成员: {Account}", member.Account);

            // 验证成员信息
            await ValidateMemberAsync(member);

            // 检查账号是否已存在
            if (await memberRepository.AccountExistsAsync(member.Account))
            {
                throw new InvalidOperationException(@$"账号 {member.Account} 已存在");
            }

            // 设置默认值
            member.CreatedAt = DateTime.Now;
            member.UpdatedAt = DateTime.Now;

            var createdMember = await memberRepository.AddAsync(member);
            logger.LogInformation(@"成员创建成功: ID={MemberId}, 账号={Account}", createdMember.Id, createdMember.Account);

            return createdMember;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"创建新成员失败: {Account}", member.Account);
            throw;
        }
    }

    /// <summary>
    /// 根据账号获取成员信息
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <returns>成员信息，不存在时返回null</returns>
    public async Task<Member?> GetMemberByAccountAsync(string account)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(account))
            {
                throw new ArgumentException(@"账号不能为空");
            }

            logger.LogInformation(@"根据账号获取成员信息: {Account}", account);
            return await memberRepository.GetByAccountAsync(account);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"根据账号获取成员信息失败: {Account}", account);
            throw;
        }
    }

    /// <summary>
    /// 更新成员信息
    /// </summary>
    /// <param name="member">成员信息</param>
    /// <returns>更新后的成员对象</returns>
    public async Task<Member> UpdateMemberAsync(Member member)
    {
        try
        {
            logger.LogInformation(@"更新成员信息: {Account}", member.Account);

            // 验证成员信息
            await ValidateMemberAsync(member);

            // 检查成员是否存在
            var existingMember = await memberRepository.GetByAccountAsync(member.Account);
            if (existingMember == null)
            {
                throw new InvalidOperationException(@$"成员 {member.Account} 不存在");
            }

            member.UpdatedAt = DateTime.Now;
            return await memberRepository.UpdateAsync(member);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"更新成员信息失败: {Account}", member.Account);
            throw;
        }
    }

    /// <summary>
    /// 检查账号是否已存在
    /// </summary>
    /// <param name="account">要检查的账号</param>
    /// <returns>存在返回true，否则返回false</returns>
    public async Task<bool> IsAccountExistsAsync(string account)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(account))
            {
                return false;
            }

            return await memberRepository.AccountExistsAsync(account);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"检查账号是否存在失败: {Account}", account);
            throw;
        }
    }

    /// <summary>
    /// 更新成员余额
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <param name="amount">变更金额（正数为增加，负数为减少）</param>
    /// <param name="financialType">财务类型</param>
    /// <param name="referenceId">关联业务ID</param>
    /// <returns>更新后的余额</returns>
    public async Task<decimal> UpdateMemberBalanceAsync(string account, decimal amount, CommandGuard.Enums.EnumFinancialType financialType, long referenceId = 0)
    {
        try
        {
            logger.LogInformation(@"更新成员余额: {Account}, 变更金额: {Amount}, 类型: {Type}", account, amount, financialType);

            if (string.IsNullOrWhiteSpace(account))
            {
                throw new ArgumentException(@"账号不能为空");
            }

            // 获取当前成员信息
            var member = await memberRepository.GetByAccountAsync(account);
            if (member == null)
            {
                throw new InvalidOperationException(@$"成员 {account} 不存在");
            }

            // 计算新余额
            var oldBalance = member.Balance;
            var newBalance = oldBalance + amount;

            // 验证余额不能为负数（除非是特殊类型的扣款）
            if (newBalance < 0 && !IsAllowNegativeBalance(financialType))
            {
                throw new InvalidOperationException(@$"余额不足，当前余额: {oldBalance}, 变更金额: {amount}");
            }

            // 更新成员余额
            var success = await memberRepository.UpdateBalanceAsync(account, newBalance);
            if (!success)
            {
                throw new InvalidOperationException(@"更新成员余额失败");
            }

            // 创建财务记录
            var financial = new Financial
            {
                Account = account,
                Type = financialType,
                Amount = amount,
                BalanceBefore = oldBalance,
                BalanceAfter = newBalance,
                ReferenceId = referenceId,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            await financialRepository.CreateFinancialRecordAsync(financial);

            logger.LogInformation(@"成员余额更新成功: {Account}, 旧余额: {OldBalance}, 新余额: {NewBalance}", account, oldBalance, newBalance);
            return newBalance;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"更新成员余额失败: {Account}", account);
            throw;
        }
    }

    /// <summary>
    /// 获取所有成员列表
    /// </summary>
    /// <returns>成员列表</returns>
    public async Task<IEnumerable<Member>> GetAllMembersAsync()
    {
        try
        {
            logger.LogInformation(@"获取所有成员列表");
            return await memberRepository.GetAllAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取所有成员列表失败");
            throw;
        }
    }

    /// <summary>
    /// 根据代理名称获取下级成员列表
    /// </summary>
    /// <param name="agentName">代理名称</param>
    /// <returns>下级成员列表</returns>
    public async Task<IEnumerable<Member>> GetMembersByAgentAsync(string agentName)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(agentName))
            {
                throw new ArgumentException(@"代理名称不能为空");
            }

            logger.LogInformation(@"根据代理名称获取下级成员列表: {AgentName}", agentName);
            return await memberRepository.GetMembersByAgentAsync(agentName);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"根据代理名称获取下级成员列表失败: {AgentName}", agentName);
            throw;
        }
    }

    /// <summary>
    /// 删除成员
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <returns>删除成功返回true，否则返回false</returns>
    public async Task<bool> DeleteMemberAsync(string account)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(account))
            {
                throw new ArgumentException(@"账号不能为空");
            }

            logger.LogInformation(@"删除成员: {Account}", account);

            // 检查成员是否存在
            var member = await memberRepository.GetByAccountAsync(account);
            if (member == null)
            {
                throw new InvalidOperationException(@$"成员 {account} 不存在");
            }

            return await memberRepository.DeleteAsync(member.Id);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"删除成员失败: {Account}", account);
            throw;
        }
    }

    /// <summary>
    /// 获取成员统计信息
    /// </summary>
    /// <returns>成员统计信息</returns>
    public async Task<(int TotalMembers, decimal TotalBalance, int ActiveMembers)> GetMemberStatisticsAsync()
    {
        try
        {
            logger.LogInformation(@"获取成员统计信息");

            var allMembers = await memberRepository.GetAllAsync();
            var memberList = allMembers.ToList();

            var totalMembers = memberList.Count;
            var totalBalance = memberList.Sum(m => m.Balance);
            var activeMembers = memberList.Count(m => m.Balance > 0);

            return (totalMembers, totalBalance, activeMembers);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"获取成员统计信息失败");
            throw;
        }
    }

    /// <summary>
    /// 验证成员余额是否足够
    /// </summary>
    /// <param name="account">成员账号</param>
    /// <param name="amount">需要的金额</param>
    /// <returns>余额足够返回true，否则返回false</returns>
    public async Task<bool> ValidateBalanceAsync(string account, decimal amount)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(account))
            {
                return false;
            }

            var member = await memberRepository.GetByAccountAsync(account);
            return member != null && member.Balance >= amount;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"验证成员余额失败: {Account}", account);
            throw;
        }
    }

    /// <summary>
    /// 搜索成员
    /// </summary>
    /// <param name="keyword">搜索关键字（账号或昵称）</param>
    /// <returns>符合条件的成员列表</returns>
    public async Task<IEnumerable<Member>> SearchMembersAsync(string keyword)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(keyword))
            {
                return new List<Member>();
            }

            logger.LogInformation(@"搜索成员: {Keyword}", keyword);

            var allMembers = await memberRepository.GetAllAsync();
            return allMembers.Where(m =>
                m.Account.Contains(keyword, StringComparison.OrdinalIgnoreCase) ||
                (!string.IsNullOrEmpty(m.Nickname) && m.Nickname.Contains(keyword, StringComparison.OrdinalIgnoreCase))
            );
        }
        catch (Exception ex)
        {
            logger.LogError(ex, @"搜索成员失败: {Keyword}", keyword);
            throw;
        }
    }

    /// <summary>
    /// 验证成员信息
    /// </summary>
    /// <param name="member">成员信息</param>
    private static async Task ValidateMemberAsync(Member member)
    {
        if (string.IsNullOrWhiteSpace(member.Account))
        {
            throw new ArgumentException(@"账号不能为空");
        }

        if (member.Account.Length < 3 || member.Account.Length > 20)
        {
            throw new ArgumentException(@"账号长度必须在3-20个字符之间");
        }

        if (member.Balance < 0)
        {
            throw new ArgumentException(@"余额不能为负数");
        }

        if (member.RebatePercentage < 0 || member.RebatePercentage > 100)
        {
            throw new ArgumentException(@"返点百分比必须在0-100之间");
        }

        await Task.CompletedTask;
    }

    /// <summary>
    /// 判断是否允许负余额
    /// </summary>
    /// <param name="financialType">财务类型</param>
    /// <returns>允许返回true，否则返回false</returns>
    private static bool IsAllowNegativeBalance(CommandGuard.Enums.EnumFinancialType financialType)
    {
        // 某些特殊类型的操作可能允许负余额，比如系统调整
        return financialType == CommandGuard.Enums.EnumFinancialType.SystemAdjustment;
    }