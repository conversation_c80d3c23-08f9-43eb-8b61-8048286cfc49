﻿using System.ComponentModel.DataAnnotations;
using CommandGuard.Enums;
using FreeSql.DataAnnotations;

namespace CommandGuard.Models;

/// <summary>
/// 财务记录实体类，对应数据库中的Financials表
///
/// 功能说明：
/// - 记录系统中所有的财务变更操作
/// - 提供完整的资金流水追踪
/// - 支持余额变更前后的完整记录
/// - 提供财务审计和对账功能
/// - 支持多种财务交易类型管理
/// - 提供关联业务的追溯能力
///
/// 业务规则：
/// - 每条记录必须关联一个有效的成员账号
/// - 记录创建后不可修改，只能查询
/// - 变更金额可以为正数（增加）或负数（减少）
/// - 必须记录变更前后的余额状态
/// - 支持关联业务ID进行追溯
/// - 所有财务操作都必须有对应的记录
/// - 提供完整的审计追踪链
///
/// 索引设计：
/// - idx_account: 按账号查询财务记录
/// - idx_type: 按交易类型查询
/// - idx_createdat: 按时间查询（审计重要）
/// - idx_businessid: 按关联业务ID查询
/// - idx_account_type: 复合索引：账号+类型
/// - idx_account_createdat: 复合索引：账号+时间
/// </summary>
[Table(Name = "Financials")]
[Index("idx_account", "Account", false)]                    // 按账号查询
[Index("idx_type", "Type", false)]                          // 按交易类型查询
[Index("idx_createdat", "CreatedAt desc", false)]           // 按时间查询
[Index("idx_businessid", "BusinessId", false)]              // 按关联业务ID查询
[Index("idx_account_type", "Account,Type", false)]          // 复合索引：账号+类型
[Index("idx_account_createdat", "Account,CreatedAt desc", false)] // 复合索引：账号+时间
public class Financial
{
    /// <summary>
    /// 财务记录唯一标识符
    /// 数据库主键，自动递增，用于唯一标识每条财务记录
    /// 数据类型：long - 64位整数，支持海量财务记录
    /// 用途：记录标识、关联查询、审计追踪等
    /// </summary>
    [Column(IsIdentity = true, IsPrimary = true)]
    public long Id { get; set; }

    /// <summary>
    /// 关联成员账号
    /// 发生财务变更的成员账号，必须是系统中存在的有效成员账号
    /// 约束：最大长度100字符，不能为空，必须关联有效的成员账号
    /// 用途：财务归属、余额追踪、账户管理、审计追踪等
    /// 关联：与Member表的Account字段关联
    /// </summary>
    [Required(ErrorMessage = @"成员账号不能为空")]
    [StringLength(100, ErrorMessage = @"成员账号长度不能超过100个字符")]
    [Column(StringLength = 100, IsNullable = false)]
    public string Account { get; set; } = string.Empty;

    /// <summary>
    /// 财务交易类型
    /// 标识财务变更的具体类型：上分、下分、回水、冻结、解冻等
    /// 枚举类型：EnumFinancialType
    /// 约束：不能为空，必须是有效的枚举值
    /// 用途：交易分类、业务区分、统计分析、审计追踪等
    /// </summary>
    [Required(ErrorMessage = @"财务类型不能为空")]
    [Column(IsNullable = false)]
    public EnumFinancialType Type { get; set; }

    /// <summary>
    /// 财务变更金额
    /// 本次财务操作的变更金额，正数表示增加，负数表示减少
    /// 数据类型：decimal(18,2) - 精度18位，小数点后2位
    /// 约束：不能为0，支持正负数，精确到分
    /// 范围：-****************.99 到 +****************.99
    /// 用途：余额计算、财务统计、审计对账等
    /// </summary>
    [Required(ErrorMessage = @"变更金额不能为空")]
    [Column(Precision = 18, Scale = 2, IsNullable = false)]
    public decimal Amount { get; set; }

    /// <summary>
    /// 变更前余额
    /// 财务操作执行前的账户余额，用于审计和对账
    /// 数据类型：decimal(18,2) - 精度18位，小数点后2位
    /// 约束：不能为空，必须≥0，记录操作前的真实余额
    /// 用途：审计追踪、余额验证、财务对账、异常检测等
    /// </summary>
    [Required(ErrorMessage = @"变更前余额不能为空")]
    [Column(Precision = 18, Scale = 2, IsNullable = false)]
    public decimal BalanceBefore { get; set; }

    /// <summary>
    /// 变更后余额
    /// 财务操作执行后的账户余额，应等于变更前余额加上变更金额
    /// 数据类型：decimal(18,2) - 精度18位，小数点后2位
    /// 约束：不能为空，必须≥0，必须满足：BalanceAfter = BalanceBefore + Amount
    /// 用途：余额验证、审计对账、数据一致性检查等
    /// </summary>
    [Required(ErrorMessage = @"变更后余额不能为空")]
    [Column(Precision = 18, Scale = 2, IsNullable = false)]
    public decimal BalanceAfter { get; set; }

    /// <summary>
    /// 关联业务ID
    /// 触发此财务变更的相关业务记录ID，用于业务追溯和关联查询
    /// 约束：可为空（某些操作可能不需要关联业务），默认值为0
    /// 示例：存取款申请ID、订单ID、返点记录ID、手动调整ID等
    /// 用途：业务追溯、关联查询、审计验证、问题排查等
    /// </summary>
    [Column(IsNullable = true)]
    public long ReferenceId { get; set; }

    /// <summary>
    /// 财务记录创建时间
    /// 财务操作发生的时间戳，用于时间排序和审计追踪
    /// 约束：不可为空，自动设置为当前时间，创建后不可修改
    /// 用途：时间排序、审计日志、统计分析、数据归档等
    /// </summary>
    [Required(ErrorMessage = @"创建时间不能为空")]
    [Column(IsNullable = false, ServerTime = DateTimeKind.Local)]
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 记录最后更新时间
    /// 财务记录最后一次修改的时间戳（通常财务记录创建后不修改）
    /// 约束：不可为空，创建时设置为当前时间
    /// 用途：数据完整性、审计追踪、异常监控等
    /// 注意：财务记录通常为只读，此字段主要用于数据完整性
    /// </summary>
    [Required(ErrorMessage = @"更新时间不能为空")]
    [Column(IsNullable = false, ServerTime = DateTimeKind.Local)]
    public DateTime UpdatedAt { get; set; } = DateTime.Now;
}