using CommandGuard.Data;
using CommandGuard.Interfaces.Repositories;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Repositories;

/// <summary>
/// 飞单订单仓储实现类，提供飞单订单相关的数据访问方法
/// </summary>
public class FlightOrderRepository(
    ILogger<FlightOrderRepository> logger,
    AppDbContext dbContext
) : BaseRepository<FlightOrder>(dbContext, logger), IFlightOrderRepository
{
    /// <summary>
    /// 根据期号获取飞单订单列表
    /// </summary>
    /// <param name="issue">投注期号</param>
    /// <returns>该期号的飞单订单列表</returns>
    public async Task<IEnumerable<FlightOrder>> GetFlightOrdersByIssueAsync(string issue)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"根据期号获取飞单订单列表: {Issue}", issue);
            return await DbContext.FlightOrders
                .Where(f => f.Issue == issue)
                .OrderBy(f => f.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据期号获取飞单订单列表失败: {Issue}", issue);
            return new List<FlightOrder>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 根据状态获取飞单订单列表
    /// </summary>
    /// <param name="status">飞单状态</param>
    /// <returns>指定状态的飞单订单列表</returns>
    public async Task<IEnumerable<FlightOrder>> GetFlightOrdersByStatusAsync(CommandGuard.Enums.EnumFlightOrderStatus status)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"根据状态获取飞单订单列表: {Status}", status);
            return await DbContext.FlightOrders
                .Where(f => f.Status == status)
                .OrderByDescending(f => f.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据状态获取飞单订单列表失败: {Status}", status);
            return new List<FlightOrder>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 根据玩法获取飞单订单列表
    /// </summary>
    /// <param name="playItem">投注玩法</param>
    /// <returns>指定玩法的飞单订单列表</returns>
    public async Task<IEnumerable<FlightOrder>> GetFlightOrdersByPlayItemAsync(string playItem)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"根据玩法获取飞单订单列表: {PlayItem}", playItem);
            return await DbContext.FlightOrders
                .Where(f => f.PlayItem == playItem)
                .OrderByDescending(f => f.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据玩法获取飞单订单列表失败: {PlayItem}", playItem);
            return new List<FlightOrder>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 获取待处理的飞单订单列表
    /// </summary>
    /// <returns>待处理的飞单订单列表</returns>
    public async Task<IEnumerable<FlightOrder>> GetPendingFlightOrdersAsync()
    {
        return await GetFlightOrdersByStatusAsync(CommandGuard.Enums.EnumFlightOrderStatus.Pending);
    }

    /// <summary>
    /// 根据期号和玩法获取飞单订单
    /// </summary>
    /// <param name="issue">投注期号</param>
    /// <param name="playItem">投注玩法</param>
    /// <returns>符合条件的飞单订单列表</returns>
    public async Task<IEnumerable<FlightOrder>> GetFlightOrdersByIssueAndPlayItemAsync(string issue, string playItem)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"根据期号和玩法获取飞单订单: {Issue}, {PlayItem}", issue, playItem);
            return await DbContext.FlightOrders
                .Where(f => f.Issue == issue && f.PlayItem == playItem)
                .OrderBy(f => f.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"根据期号和玩法获取飞单订单失败: {Issue}, {PlayItem}", issue, playItem);
            return new List<FlightOrder>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 获取指定时间范围内的飞单订单
    /// </summary>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>符合条件的飞单订单列表</returns>
    public async Task<IEnumerable<FlightOrder>> GetFlightOrdersByDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"获取指定时间范围内的飞单订单: {StartDate} - {EndDate}", startDate, endDate);
            return await DbContext.FlightOrders
                .Where(f => f.CreatedAt >= startDate && f.CreatedAt <= endDate)
                .OrderByDescending(f => f.CreatedAt)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"获取指定时间范围内的飞单订单失败: {StartDate} - {EndDate}", startDate, endDate);
            return new List<FlightOrder>();
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 更新飞单状态
    /// </summary>
    /// <param name="flightOrderId">飞单订单ID</param>
    /// <param name="status">新状态</param>
    /// <returns>更新成功返回true，否则返回false</returns>
    public async Task<bool> UpdateFlightOrderStatusAsync(long flightOrderId, CommandGuard.Enums.EnumFlightOrderStatus status)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"更新飞单状态: {FlightOrderId}, {Status}", flightOrderId, status);
            var affectedRows = await DbContext.UpdateFlightOrder
                .Set(f => f.Status, status)
                .Where(f => f.Id == flightOrderId)
                .ExecuteAffrowsAsync();
            return affectedRows > 0;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"更新飞单状态失败: {FlightOrderId}", flightOrderId);
            return false;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 批量更新飞单状态
    /// </summary>
    /// <param name="flightOrderIds">飞单订单ID列表</param>
    /// <param name="status">新状态</param>
    /// <returns>更新成功的数量</returns>
    public async Task<int> BatchUpdateFlightOrderStatusAsync(IEnumerable<long> flightOrderIds, CommandGuard.Enums.EnumFlightOrderStatus status)
    {
        await Semaphore.WaitAsync();
        try
        {
            var idList = flightOrderIds.ToList();
            Logger.LogInformation(@"批量更新飞单状态，数量: {Count}, 状态: {Status}", idList.Count, status);

            var affectedRows = await DbContext.UpdateFlightOrder
                .Set(f => f.Status, status)
                .Where(f => idList.Contains(f.Id))
                .ExecuteAffrowsAsync();

            return affectedRows;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"批量更新飞单状态失败");
            return 0;
        }
        finally
        {
            Semaphore.Release();
        }
    }

    /// <summary>
    /// 获取飞单统计信息
    /// </summary>
    /// <param name="startDate">开始时间</param>
    /// <param name="endDate">结束时间</param>
    /// <returns>飞单统计信息</returns>
    public async Task<(int PendingCount, int ProcessingCount, int SuccessCount, int FailedCount, decimal TotalAmount)> GetFlightOrderStatisticsAsync(DateTime startDate, DateTime endDate)
    {
        await Semaphore.WaitAsync();
        try
        {
            Logger.LogInformation(@"获取飞单统计信息: {StartDate} - {EndDate}", startDate, endDate);

            var orders = await DbContext.FlightOrders
                .Where(f => f.CreatedAt >= startDate && f.CreatedAt <= endDate)
                .ToListAsync();

            var pendingCount = orders.Count(f => f.Status == CommandGuard.Enums.EnumFlightOrderStatus.Pending);
            var processingCount = orders.Count(f => f.Status == CommandGuard.Enums.EnumFlightOrderStatus.Processing);
            var successCount = orders.Count(f => f.Status == CommandGuard.Enums.EnumFlightOrderStatus.Success);
            var failedCount = orders.Count(f => f.Status == CommandGuard.Enums.EnumFlightOrderStatus.Failed);
            var totalAmount = orders.Sum(f => f.Amount);

            return (pendingCount, processingCount, successCount, failedCount, totalAmount);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, @"获取飞单统计信息失败");
            return (0, 0, 0, 0, 0);
        }
        finally
        {
            Semaphore.Release();
        }
    }
